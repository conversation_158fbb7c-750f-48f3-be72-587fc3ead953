import { createServer } from 'http';
import webSocketService from './services/websocket.service';

const PORT = process.env.WS_PORT || 3001;

// Create HTTP server for WebSocket
const server = createServer();

// Initialize WebSocket service
webSocketService.initialize(server);

// Start server
server.listen(PORT, () => {
  console.log(`🚀 WebSocket server running on port ${PORT}`);
  console.log(`📡 WebSocket URL: ws://localhost:${PORT}`);
});

// Graceful shutdown
process.on('SIGTERM', () => {
  console.log('🛑 Shutting down WebSocket server...');
  webSocketService.close();
  server.close(() => {
    console.log('✅ WebSocket server closed');
    process.exit(0);
  });
});

process.on('SIGINT', () => {
  console.log('🛑 Shutting down WebSocket server...');
  webSocketService.close();
  server.close(() => {
    console.log('✅ WebSocket server closed');
    process.exit(0);
  });
});

export default server;
