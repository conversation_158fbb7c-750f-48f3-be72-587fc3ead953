-- AlterTable
ALTER TABLE `Bid` ADD COLUMN `auctionWon` BOOLEAN NOT NULL DEFAULT false,
    ADD COLUMN `winnerNotified` BOOLEAN NOT NULL DEFAULT false,
    ADD COLUMN `winnerNotifiedAt` DATETIME(3) NULL;

-- AlterTable
ALTER TABLE `Product` ADD COLUMN `auctionCompleted` BOOLEAN NOT NULL DEFAULT false,
    ADD COLUMN `auctionCompletedAt` DATETIME(3) NULL;

-- CreateIndex
CREATE INDEX `Bid_isWinning_idx` ON `Bid`(`isWinning`);

-- CreateIndex
CREATE INDEX `Bid_auctionWon_idx` ON `Bid`(`auctionWon`);

-- CreateIndex
CREATE INDEX `Product_auctionCompleted_idx` ON `Product`(`auctionCompleted`);

-- CreateIndex
CREATE INDEX `Product_auctionEndDate_idx` ON `Product`(`auctionEndDate`);
