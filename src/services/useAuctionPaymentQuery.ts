import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { useAuthenticatedApi } from "./useAuthQuery";
import { toaster } from "@/components/ui/toaster";

// Types for auction payment
export interface AuctionPaymentData {
  bidId: string;
  productId: string;
  winningBid: number;
  shippingAddress: {
    name: string;
    address: string;
    city: string;
    provinceRegion: string;
    zipCode: string;
    country: string;
  };
  paymentMethod: string;
  selectedPaymentMethod?: string;
  ewalletType?: string;
  paymentChannel?: string;
  bankCode?: string;
  retailOutletName?: string;
  shippingOption?: any;
  currency: 'USD' | 'IDR';
  notes?: string;
}

export interface AuctionPaymentResponse {
  success: boolean;
  orderId: string;
  orderNumber: string;
  paymentUrl?: string;
  invoiceUrl?: string;
  paymentId?: string;
  message: string;
}

export interface AuctionWinnerDetails {
  id: string;
  bidId: string;
  productId: string;
  productName: string;
  productSlug?: string;
  productImage?: string;
  winningBid: number;
  auctionEndDate: string;
  paymentDeadline: string;
  paymentStatus: 'pending' | 'paid' | 'failed' | 'expired';
  orderId?: string;
  orderNumber?: string;
  isPaymentOverdue: boolean;
  hoursRemaining: number;
}

// Query Keys
export const auctionPaymentQueryKeys = {
  all: ['auction-payment'] as const,
  winnerDetails: (bidId: string) => [...auctionPaymentQueryKeys.all, 'winner-details', bidId] as const,
  winnerByProduct: (productId: string) => [...auctionPaymentQueryKeys.all, 'winner-by-product', productId] as const,
  paymentStatus: (orderId: string) => [...auctionPaymentQueryKeys.all, 'payment-status', orderId] as const,
};

// Custom hook for fetching auction winner details by bidId
export const useAuctionWinnerDetailsQuery = (bidId: string) => {
  const api = useAuthenticatedApi();

  return useQuery({
    queryKey: auctionPaymentQueryKeys.winnerDetails(bidId),
    queryFn: async (): Promise<AuctionWinnerDetails> => {
      const response = await api.get(`/auction/winner-details/${bidId}`);
      return response.data.data;
    },
    enabled: !!api && !!bidId,
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchInterval: 30000, // Refetch every 30 seconds to check payment deadline
  });
};

// Custom hook for fetching auction winner details by productId
export const useAuctionWinnerByProductQuery = (productId: string) => {
  const api = useAuthenticatedApi();

  return useQuery({
    queryKey: auctionPaymentQueryKeys.winnerByProduct(productId),
    queryFn: async (): Promise<AuctionWinnerDetails> => {
      try {
        const response = await api.get(`/auction/winner-by-product/${productId}`);
        return response.data.data;
      } catch (error) {
        // Fallback: try to get from bidding data
        try {
          const bidsResponse = await api.get('/bidding/user-bids');
          const userBids = bidsResponse.data.data.bids || [];

          // Find winning bid for this product
          const winningBid = userBids.find((bid: any) =>
            bid.productId === productId && bid.auctionStatus === 'won'
          );

          if (winningBid) {
            const now = new Date();
            const auctionEndDate = new Date(winningBid.product.auctionEndDate || now);
            const paymentDeadline = new Date(auctionEndDate.getTime() + (3 * 24 * 60 * 60 * 1000)); // 3 days after auction end

            return {
              id: winningBid.id || `winner-${productId}`,
              bidId: winningBid.id || '',
              productId: productId,
              productName: winningBid.product.itemName,
              productSlug: winningBid.product.slug,
              productImage: winningBid.product.images?.find((img: any) => img.isMain)?.imageUrl,
              winningBid: winningBid.highestBid,
              auctionEndDate: winningBid.product.auctionEndDate || now.toISOString(),
              paymentDeadline: paymentDeadline.toISOString(),
              paymentStatus: 'pending' as const,
              isPaymentOverdue: now > paymentDeadline,
              hoursRemaining: Math.max(0, Math.floor((paymentDeadline.getTime() - now.getTime()) / (1000 * 60 * 60)))
            };
          }
        } catch (fallbackError) {
          // If both fail, throw the original error
        }

        throw error;
      }
    },
    enabled: !!api && !!productId,
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchInterval: 30000, // Refetch every 30 seconds to check payment deadline
  });
};

// Custom hook for processing auction payment
export const useAuctionPaymentMutation = () => {
  const api = useAuthenticatedApi();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (paymentData: AuctionPaymentData): Promise<AuctionPaymentResponse> => {
      const response = await api.post('/auction/process-payment', paymentData);
      return response.data.data;
    },
    onSuccess: (data, variables) => {
      // Invalidate related queries
      queryClient.invalidateQueries({ 
        queryKey: auctionPaymentQueryKeys.winnerDetails(variables.bidId) 
      });
      
      // Invalidate bidding queries
      queryClient.invalidateQueries({ queryKey: ['bidding'] });
      queryClient.invalidateQueries({ queryKey: ['order-tracking'] });
      
      toaster.create({
        title: "Payment Processed Successfully",
        description: `Order #${data.orderNumber} has been created. ${data.paymentUrl ? 'Redirecting to payment...' : 'Payment completed!'}`,
        type: "success",
        duration: 5000,
      });

      // Redirect to payment if payment URL is provided
      if (data.paymentUrl) {
        setTimeout(() => {
          window.open(data.paymentUrl, '_blank');
        }, 1000);
      }
    },
    onError: (error: any) => {
      toaster.create({
        title: "Payment Processing Failed",
        description: error.response?.data?.message || "An error occurred while processing your payment",
        type: "error",
        duration: 5000,
      });
    },
  });
};

// Custom hook for checking payment status
export const useAuctionPaymentStatusQuery = (orderId: string) => {
  const api = useAuthenticatedApi();

  return useQuery({
    queryKey: auctionPaymentQueryKeys.paymentStatus(orderId),
    queryFn: async (): Promise<{
      status: string;
      isPaid: boolean;
      paymentMethod: string;
      paidAt?: string;
      failureReason?: string;
    }> => {
      const response = await api.get(`/auction/payment-status/${orderId}`);
      return response.data.data;
    },
    enabled: !!api && !!orderId,
    refetchInterval: (data) => {
      // Stop refetching if payment is completed or failed
      if (data?.isPaid || data?.status === 'failed') {
        return false;
      }
      return 10000; // Refetch every 10 seconds for pending payments
    },
  });
};

// Custom hook for creating auction order without immediate payment
export const useCreateAuctionOrderMutation = () => {
  const api = useAuthenticatedApi();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (orderData: {
      bidId: string;
      productId: string;
      winningBid: number;
      shippingAddress: any;
      notes?: string;
    }): Promise<{
      orderId: string;
      orderNumber: string;
      paymentDeadline: string;
    }> => {
      const response = await api.post('/auction/create-order', orderData);
      return response.data.data;
    },
    onSuccess: (data, variables) => {
      // Invalidate related queries
      queryClient.invalidateQueries({ 
        queryKey: auctionPaymentQueryKeys.winnerDetails(variables.bidId) 
      });
      
      toaster.create({
        title: "Order Created Successfully",
        description: `Order #${data.orderNumber} has been created. Complete payment before the deadline.`,
        type: "success",
        duration: 5000,
      });
    },
    onError: (error: any) => {
      toaster.create({
        title: "Failed to Create Order",
        description: error.response?.data?.message || "An error occurred while creating the order",
        type: "error",
        duration: 5000,
      });
    },
  });
};

// Custom hook for extending payment deadline (if allowed)
export const useExtendPaymentDeadlineMutation = () => {
  const api = useAuthenticatedApi();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ bidId, reason }: { bidId: string; reason?: string }): Promise<{
      newDeadline: string;
      extensionHours: number;
    }> => {
      const response = await api.post(`/auction/extend-payment-deadline/${bidId}`, { reason });
      return response.data.data;
    },
    onSuccess: (data, variables) => {
      // Invalidate related queries
      queryClient.invalidateQueries({ 
        queryKey: auctionPaymentQueryKeys.winnerDetails(variables.bidId) 
      });
      
      toaster.create({
        title: "Payment Deadline Extended",
        description: `Your payment deadline has been extended by ${data.extensionHours} hours.`,
        type: "success",
        duration: 5000,
      });
    },
    onError: (error: any) => {
      toaster.create({
        title: "Extension Request Failed",
        description: error.response?.data?.message || "Unable to extend payment deadline",
        type: "error",
        duration: 5000,
      });
    },
  });
};

// Custom hook for cancelling auction order (if within allowed timeframe)
export const useCancelAuctionOrderMutation = () => {
  const api = useAuthenticatedApi();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ bidId, reason }: { bidId: string; reason?: string }): Promise<void> => {
      await api.post(`/auction/cancel-order/${bidId}`, { reason });
    },
    onSuccess: (_, variables) => {
      // Invalidate related queries
      queryClient.invalidateQueries({ 
        queryKey: auctionPaymentQueryKeys.winnerDetails(variables.bidId) 
      });
      queryClient.invalidateQueries({ queryKey: ['bidding'] });
      
      toaster.create({
        title: "Order Cancelled",
        description: "Your auction order has been cancelled successfully.",
        type: "info",
        duration: 5000,
      });
    },
    onError: (error: any) => {
      toaster.create({
        title: "Cancellation Failed",
        description: error.response?.data?.message || "Unable to cancel the order",
        type: "error",
        duration: 5000,
      });
    },
  });
};

// Utility functions
export const calculateTimeRemaining = (deadline: string): {
  hours: number;
  minutes: number;
  isOverdue: boolean;
  isUrgent: boolean;
} => {
  const now = new Date();
  const deadlineDate = new Date(deadline);
  const diff = deadlineDate.getTime() - now.getTime();
  
  if (diff <= 0) {
    return { hours: 0, minutes: 0, isOverdue: true, isUrgent: true };
  }
  
  const hours = Math.floor(diff / (1000 * 60 * 60));
  const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
  
  return {
    hours,
    minutes,
    isOverdue: false,
    isUrgent: hours <= 24, // Urgent if less than 24 hours remaining
  };
};

export const formatPaymentDeadline = (deadline: string): string => {
  const { hours, minutes, isOverdue } = calculateTimeRemaining(deadline);
  
  if (isOverdue) return 'Payment overdue';
  
  if (hours > 24) {
    const days = Math.floor(hours / 24);
    const remainingHours = hours % 24;
    return `${days}d ${remainingHours}h remaining`;
  }
  
  if (hours > 0) {
    return `${hours}h ${minutes}m remaining`;
  }
  
  return `${minutes}m remaining`;
};

export default {
  useAuctionWinnerDetailsQuery,
  useAuctionWinnerByProductQuery,
  useAuctionPaymentMutation,
  useAuctionPaymentStatusQuery,
  useCreateAuctionOrderMutation,
  useExtendPaymentDeadlineMutation,
  useCancelAuctionOrderMutation,
  calculateTimeRemaining,
  formatPaymentDeadline,
};
