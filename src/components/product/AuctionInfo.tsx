"use client"
import React, { useState, useEffect } from 'react'
import {
    Box,
    Button,
    Text,
    VStack,
    HStack,
    Badge,
    Flex,
    createListColl<PERSON>tion,
    Icon,
    Skeleton,
} from '@chakra-ui/react'
import { FaClock, FaHistory, FaSyncAlt } from 'react-icons/fa'
import { useCurrencyLanguage } from '@/contexts/CurrencyLanguageContext'
import {
    usePlaceBidMutation,
    useUserBidQuery,
    useBidHistoryQuery,
    useAutoBidMutation,
    useCancelAutoBidMutation,
    useUserAutoBidQuery
} from '@/services/useBiddingQuery'
import ClientOnly from '@/components/ui/ClientOnly'
import { ChakraSelect } from '../ui/select/ChakraSelect'
import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import { useTranslations } from 'next-intl'
import { toaster } from '@/components/ui/toaster'
// import { useWebSocket } from '@/hooks/useWebSocket'

interface AuctionInfoProps {
    currentBid: number;
    startingPrice: number;
    bidCount: number;
    auctionStartDate: string;
    auctionEndDate: string;
    extendedBiddingEnabled?: boolean;
    extendedBiddingMinutes?: number;
    extendedBiddingDuration?: number;
    productId: string;
    productName: string;
    onPlaceBid?: (productId: string, bidAmount: number) => void;
    onAddToWishlist?: (productId: string) => void;
    isInWishlist?: boolean;
    onShowBidHistory?: (productId: string) => void;
    mb?: number;
    isLoadingAuth?: boolean;
    isAuthenticated?: boolean;
    auctionStatus?: 'active' | 'ended' | 'upcoming'; // Added auctionStatus prop
    timeLeft?: string; // Added timeLeft prop for auction status
}

const AuctionInfo: React.FC<AuctionInfoProps> = ({
    currentBid,
    startingPrice,
    bidCount,
    extendedBiddingEnabled = false,
    extendedBiddingMinutes,
    extendedBiddingDuration,
    productId,
    onShowBidHistory,
    isLoadingAuth = false,
    isAuthenticated = false,
    auctionStatus = 'active',
    timeLeft = 'N/A',
    mb = 0
}) => {
    const router = useRouter();
    const dataSession = useSession()
    const t = useTranslations()
    const { formatPrice, convertPrice, currency } = useCurrencyLanguage()

    // Regular bidding state
    const [bidAmount, setBidAmount] = useState(currentBid + 1);

    // Auto-bid state
    const [autoBidEnabled, setAutoBidEnabled] = useState(false);
    const [startingBid, setStartingBid] = useState(currentBid + 1); // Starting bid amount
    const [maxBudget, setMaxBudget] = useState(currentBid + 100);
    const [bidIncrement, setBidIncrement] = useState(5);
    const [bidHistoryOpen, setBidHistoryOpen] = useState({
        open: true,
        product: undefined
    });

    // Bidding mode state
    const [biddingMode, setBiddingMode] = useState<'bid_now' | 'auto_extend'>('bid_now');

    const placeBidMutation = usePlaceBidMutation();
    const autoBidMutation = useAutoBidMutation();
    const cancelAutoBidMutation = useCancelAutoBidMutation();
    const { data: userBid } = useUserBidQuery(productId, dataSession?.data?.user?.id || '');
    const { data: bidHistory } = useBidHistoryQuery(productId);
    const { data: userAutoBid } = useUserAutoBidQuery(productId);

    // WebSocket for real-time updates including auction extensions
    // useWebSocket({
    //     productId,
    //     onMessage: (message) => {
    //         if (message.type === 'broadcast' && message.data?.type === 'auction_extended') {
    //             const extensionData = message.data.data;
    //             toaster.create({
    //                 title: "Auction Extended!",
    //                 description: `Auction has been extended by ${extensionData.extendedMinutes} minutes due to recent bidding activity.`,
    //                 type: "info",
    //                 duration: 5000,
    //             });
    //         }
    //     }
    // });

    const minBidIncrement = Math.max(1, Math.floor(currentBid * 0.05)); // 5% increment or $1 minimum
    const minBidAmount = currentBid + minBidIncrement;

    useEffect(() => {
        setBidAmount(minBidAmount);
    }, [minBidAmount]);

    // Sync auto-bid state with server data
    useEffect(() => {
        if (userAutoBid) {
            setAutoBidEnabled(userAutoBid.isActive);
            setMaxBudget(userAutoBid.maxBudget);
            setBidIncrement(userAutoBid.bidIncrement);

            // If auto bid is active, switch to auto_extend mode and disable bid_now
            if (userAutoBid.isActive) {
                setBiddingMode('auto_extend');
            }
        }
    }, [userAutoBid]);

    // Check if user has placed any manual bids
    // const hasManualBids = userBid && !userAutoBid?.isActive;

    // Determine if bid now should be disabled
    // Only disable if auto bid is currently active (not just enabled in the past)
    const isBidNowDisabled = autoBidEnabled && (userAutoBid?.isActive);

    // Determine if auto bid should be disabled
    // Allow auto bid even if user has manual bids, but warn them
    // const isAutoBidDisabled = false; // Always allow auto bid activation



    // Generate bid amount options from minimum to $1,000,000 (in USD)
    const generateBidOptions = () => {
        const options = [];
        let currentAmount = minBidAmount;

        // Add incremental options up to $1,000,000
        while (currentAmount <= 1000000) {
            options.push(currentAmount);

            if (currentAmount < 100) {
                currentAmount += 5; // $5 increments under $100
            } else if (currentAmount < 1000) {
                currentAmount += 25; // $25 increments under $1,000
            } else if (currentAmount < 10000) {
                currentAmount += 100; // $100 increments under $10,000
            } else if (currentAmount < 100000) {
                currentAmount += 500; // $500 increments under $100,000
            } else {
                currentAmount += 1000; // $1,000 increments above $100,000
            }
        }

        return options;
    };

    const bidOptions = generateBidOptions();

    const bidOptionsSelect = createListCollection({
        items: bidOptions.map(amount => ({
            label: formatPrice(convertPrice(amount, 'USD'), currency), // Display in user's currency
            value: amount.toString() // Keep USD value for backend
        })),
    })

    const handlePlaceBid = async () => {
        console.log('Placing bid:', bidAmount, 'USD for product:', productId);
        if (bidAmount >= minBidAmount) {
            try {
                // Send bid amount in USD to backend
                await placeBidMutation.mutateAsync({
                    productId,
                    bidAmount, // This is already in USD
                    bidType: 'manual'
                });
                // Reset bid amount to new minimum after successful bid
                setBidAmount(bidAmount + minBidIncrement);
            } catch (error) {
                console.error('Failed to place bid:', error);
            }
        }
    };

    const handleEnableAutoBid = async () => {
        // Validation
        if (startingBid <= currentBid) {
            toaster.create({
                title: "Invalid Starting Bid",
                description: `Starting bid must be higher than current bid (${formatPrice(currentBid)})`,
                type: "error",
            });
            return;
        }

        if (maxBudget <= startingBid) {
            toaster.create({
                title: "Invalid Max Budget",
                description: `Max budget must be higher than starting bid (${formatPrice(startingBid)})`,
                type: "error",
            });
            return;
        }

        if (bidIncrement <= 0) {
            toaster.create({
                title: "Invalid Bid Increment",
                description: 'Bid increment must be greater than 0',
                type: "error",
            });
            return;
        }

        if (bidIncrement >= (maxBudget - startingBid)) {
            toaster.create({
                title: "Invalid Bid Increment",
                description: 'Bid increment is too large for the budget range',
                type: "error",
            });
            return;
        }

        // const minIncrement = Math.max(1, Math.floor(currentBid * 0.01)); // 1% of current bid or $1 minimum
        // if (bidIncrement < minIncrement) {
        //     toaster.create({
        //         title: "Invalid Bid Increment",
        //         description: `Bid increment must be at least ${formatPrice(minIncrement, 'USD')}`,
        //         type: "error",
        //     });
        //     return;
        // }

        try {
            console.log('Enabling auto-bid with (USD):', { productId, startingBid, maxBudget, bidIncrement });
            // Send auto-bid settings in USD to backend
            const result = await autoBidMutation.mutateAsync({
                productId,
                startingBid, // Already in USD
                maxBudget, // Already in USD
                bidIncrement // Already in USD
            });
            console.log('Auto-bid enabled successfully:', result);
            setAutoBidEnabled(true);

            toaster.create({
                title: "Auto-bid Enabled",
                description: `Auto-bid activated with max budget of ${formatPrice(convertPrice(maxBudget, 'USD'), currency)}`,
                type: "success",
            });
        } catch (error) {
            console.error('Failed to enable auto-bid:', error);
            toaster.create({
                title: "Auto-bid Failed",
                description: error instanceof Error ? error.message : "Failed to enable auto-bidding",
                type: "error",
            });
        }
    };

    const handleDisableAutoBid = async () => {
        try {
            await cancelAutoBidMutation.mutateAsync(productId);
            setAutoBidEnabled(false);

            // Allow user to switch back to manual bidding
            // Note: This does NOT cancel any bids already placed by auto-bid
            // User keeps their current bid position
            setBiddingMode('bid_now');

            toaster.create({
                title: "Auto-bid Disabled",
                description: "Auto-bidding has been disabled. Your current bids remain active.",
                type: "info",
            });
        } catch (error) {
            console.error('Failed to disable auto-bid:', error);
            toaster.create({
                title: "Error",
                description: "Failed to disable auto-bidding. Please try again.",
                type: "error",
            });
        }
    };



    const getStatusColor = () => {
        switch (auctionStatus) {
            case 'upcoming': return 'blue';
            case 'active': return 'green';
            case 'ended': return 'gray';
            default: return 'gray';
        }
    };

    const getStatusText = () => {
        switch (auctionStatus) {
            case 'upcoming': return 'Upcoming';
            case 'active': return 'Live Auction';
            case 'ended': return 'Auction Ended';
            default: return 'Unknown';
        }
    };

    return (
        <Box
            bg="white"
            p={6}
            borderRadius="lg"
            border="1px solid"
            borderColor="gray.200"
            mb={mb}
        >
            <VStack align="stretch" gap={4}>
                {/* Auction Status */}
                <Flex justify="space-between" align="center">
                    <Badge colorScheme={getStatusColor()} variant="solid" fontSize="sm" px={3} py={1}>
                        {getStatusText()}
                    </Badge>
                    <HStack>
                        <FaClock />
                        <ClientOnly fallback={<Text fontSize="sm" color="gray.600">Loading...</Text>}>
                            <Text fontSize="sm" color="gray.600">
                                {timeLeft}
                            </Text>
                        </ClientOnly>
                    </HStack>
                </Flex>

                {/* Current Bid Display */}
                <Box textAlign="center" py={6}>
                    <Text fontSize="sm" color="gray.600" mb={3} textTransform="uppercase" letterSpacing="wide">
                        {t('Product.currentBid')}
                    </Text>
                    <Text fontSize="5xl" fontWeight="extrabold" color="gray.800" lineHeight="1">
                        {formatPrice(convertPrice(currentBid, 'USD'), currency)}
                    </Text>
                    <HStack justify="center" align="center" mt={3} gap={4}>
                        <Text fontSize="sm" color="gray.500">
                            {bidCount} {t('Product.bidCount')} • {t('Product.startingBid')}: {formatPrice(convertPrice(startingPrice, 'USD'), currency)}
                        </Text>
                        {bidHistory && bidHistory.bids.length > 0 && (
                            <Button
                                size="xs"
                                variant="ghost"
                                colorScheme="blue"
                                onClick={() => onShowBidHistory?.(productId)}
                            >
                                <FaHistory style={{ marginRight: '4px' }} />
                                History
                            </Button>
                        )}
                    </HStack>
                </Box>
                {(!bidHistory || bidHistory.bids.length === 0) && (
                    <Box
                        p={4}
                        bg="gray.50"
                        borderRadius="lg"
                        border="1px solid"
                        borderColor="gray.200"
                        textAlign="center"
                    >
                        <VStack gap={3}>
                            <Box
                                w="48px"
                                h="48px"
                                bg="gray.300"
                                borderRadius="full"
                                display="flex"
                                alignItems="center"
                                justifyContent="center"
                                mx="auto"
                            >
                                <FaHistory color="white" size="20px" />
                            </Box>
                            <VStack gap={1}>
                                <Text fontSize="md" fontWeight="medium" color="gray.700">
                                    No Bids Yet
                                </Text>
                                <Text fontSize="sm" color="gray.600">
                                    Be the first to place a bid on this auction!
                                </Text>
                            </VStack>
                        </VStack>
                    </Box>
                )}

                {/* User's Bid Status */}
                {userBid && (
                    <Box p={3} bg={userBid.isWinning ? "green.50" : "orange.50"} borderRadius="md">
                        <Text fontSize="sm" color={userBid.isWinning ? "green.700" : "orange.700"}>
                            {userBid.isWinning ? `🎉 ${t('Product.highestBidder')}` : `⚠️ ${t('Product.outbid')}`}
                        </Text>
                        <Text fontSize="sm" color="gray.600">
                            {t('Auction.bidAmount')}: <Box as="span" fontWeight="bold" color="gray.800">{formatPrice(convertPrice(userBid.amount, 'USD'), currency)}</Box>
                        </Text>
                    </Box>
                )}

                {/* Extended Bidding Info */}
                {extendedBiddingEnabled && auctionStatus === 'active' && (
                    <Box p={3} bg="blue.50" borderRadius="md" border="1px solid" borderColor="blue.200">
                        <VStack align="start" gap={1}>
                            <Text fontSize="sm" fontWeight="medium" color="blue.800">
                                <Icon as={FaSyncAlt} mr="2" /> Extended Bidding Enabled
                            </Text>
                            <Text fontSize="xs" color="blue.700">
                                Auction will be extended by {extendedBiddingDuration || 'N/A'} minutes if bids are placed in the final {extendedBiddingMinutes || 'N/A'} minutes.
                            </Text>
                        </VStack>
                    </Box>
                )}

                {/* Bid Placement */}
                {auctionStatus === 'active' && (
                    <VStack align="stretch" gap={4}>
                        {/* Bidding Mode Selection */}
                        <VStack align="stretch" gap={2}>
                            <Text fontSize="sm" fontWeight="medium" color="gray.700">
                                Choose Bidding Mode
                            </Text>
                            <HStack gap={4}>
                                <Button
                                    variant={biddingMode === 'bid_now' ? 'solid' : 'outline'}
                                    colorScheme="blue"
                                    size="sm"
                                    onClick={() => setBiddingMode('bid_now')}
                                    flex={1}
                                >
                                    Bid Now
                                </Button>
                                <Button
                                    variant={biddingMode === 'auto_extend' ? 'solid' : 'outline'}
                                    colorScheme="green"
                                    size="sm"
                                    onClick={() => setBiddingMode('auto_extend')}
                                    flex={1}
                                >
                                    Auto Extend
                                </Button>
                            </HStack>
                        </VStack>

                        {/* Bid Now Section */}
                        {biddingMode === 'bid_now' && (
                            <Box>
                                <Text fontSize="sm" color="gray.600" mb={2}>
                                    {t('Auction.minimumBid')}: <Box as="span" fontWeight="bold" color="gray.800">{formatPrice(convertPrice(minBidAmount, 'USD'))}</Box>
                                </Text>

                                {/* Auto-bid active warning */}
                                {isBidNowDisabled && (
                                    <Box p={3} bg="orange.50" borderRadius="md" border="1px solid" borderColor="orange.200" mb={3}>
                                        <Text fontSize="sm" color="orange.700" fontWeight="medium">
                                            🤖 Auto-bid is currently active. Manual bidding is disabled.
                                        </Text>
                                        <Text fontSize="xs" color="orange.600" mt={1}>
                                            Disable auto-bid to place manual bids.
                                        </Text>
                                    </Box>
                                )}

                                {
                                    isLoadingAuth ? (
                                        <Skeleton
                                            width="full"
                                            height="30px"
                                            borderRadius="lg" />

                                    ) :
                                        isAuthenticated ? (
                                            <HStack gap="4">
                                                <ChakraSelect
                                                    width='60%'
                                                    size='lg'
                                                    placeholder={t('Product.bidNow')}
                                                    collection={bidOptionsSelect}
                                                    lazyMount={true}
                                                    disabled={isBidNowDisabled}
                                                    onValueChange={(value) => {
                                                        if (!isBidNowDisabled) {
                                                            console.log('Selected bid amount:', value);
                                                            setBidAmount(value.value?.[0] ? parseFloat(value.value[0]) : 0);
                                                        }
                                                    }}
                                                />
                                                <Button
                                                    size={'lg'}
                                                    colorScheme="blue"
                                                    onClick={handlePlaceBid}
                                                    loading={placeBidMutation.isPending}
                                                    disabled={placeBidMutation.isPending || bidAmount < minBidAmount || isBidNowDisabled}
                                                    w="40%"
                                                    borderRadius={'full'}
                                                >
                                                    {t('Product.bidNow')}
                                                </Button>
                                            </HStack>
                                        ) : (
                                            <Button
                                                size={'lg'}
                                                colorScheme="blue"
                                                w="full"
                                                borderRadius={'full'}
                                                onClick={() => router.push('/auth/login')}
                                                disabled={isBidNowDisabled}
                                            >
                                                Login to Buy / Bid
                                            </Button>

                                        )
                                }

                                {bidAmount < minBidAmount && !isBidNowDisabled && (
                                    <Text fontSize="xs" color="red.500" mt={1}>
                                        {t('Auction.bidTooLow')}
                                    </Text>
                                )}
                            </Box>
                        )}

                        {/* Auto Extend Section */}
                        {biddingMode === 'auto_extend' && (
                            <Box>
                                <Text fontSize="sm" color="gray.600" mb={3}>
                                    Set your maximum budget and increment amount. When someone bids higher than you, the system will automatically place a bid on your behalf up to your maximum budget.
                                </Text>

                                {isAuthenticated ? (
                                    <VStack align="stretch" gap={3}>
                                        {/* Auto-bid Status */}
                                        {autoBidEnabled && userAutoBid && (
                                            <Box p={4} bg="green.50" borderRadius="lg" border="1px solid" borderColor="green.200">
                                                <VStack align="start" gap={2}>
                                                    <HStack>
                                                        <Icon color="green.600">
                                                            <FaSyncAlt />
                                                        </Icon>
                                                        <Text fontSize="sm" color="green.700" fontWeight="bold">
                                                            {t('Auction.autoBidActive')}
                                                        </Text>
                                                    </HStack>
                                                    <VStack align="start" gap={1} fontSize="xs" color="green.600">
                                                        <Text>
                                                            <strong>Current Bid:</strong> {formatPrice(convertPrice(currentBid, 'USD'), currency)}
                                                        </Text>
                                                        <Text>
                                                            <strong>Starting Bid:</strong> {formatPrice(convertPrice(userAutoBid.startingBid || currentBid + userAutoBid.bidIncrement, 'USD'), currency)}
                                                        </Text>
                                                        <Text>
                                                            <strong>Next Auto-bid:</strong> {formatPrice(convertPrice(Math.max(currentBid + userAutoBid.bidIncrement, userAutoBid.startingBid || currentBid + userAutoBid.bidIncrement), 'USD'), currency)}
                                                        </Text>
                                                        <Text>
                                                            <strong>Bid Increment:</strong> {formatPrice(convertPrice(userAutoBid.bidIncrement, 'USD'), currency)}
                                                        </Text>
                                                        <Text>
                                                            <strong>Max Budget:</strong> {formatPrice(convertPrice(userAutoBid.maxBudget, 'USD'), currency)}
                                                        </Text>
                                                        <Text>
                                                            <strong>Remaining Budget:</strong> {formatPrice(convertPrice(Math.max(0, userAutoBid.maxBudget - currentBid), 'USD'), currency)}
                                                        </Text>
                                                        <Text>
                                                            <strong>Status:</strong> {userAutoBid.isActive ? 'Active' : 'Inactive'}
                                                        </Text>
                                                        <Text color="gray.500">
                                                            Auto-bid will start from {formatPrice(convertPrice(userAutoBid.startingBid || currentBid + userAutoBid.bidIncrement, 'USD'), currency)} and place bids up to your max budget
                                                        </Text>
                                                    </VStack>
                                                </VStack>
                                            </Box>
                                        )}

                                        {/* Auto-bid Settings */}
                                        {!autoBidEnabled && (
                                            <VStack align="stretch" gap={3}>
                                                <Box>
                                                    <Text fontSize="xs" color="gray.600" mb={1}>Starting Bid Amount</Text>
                                                    <ChakraSelect
                                                        size='md'
                                                        placeholder={formatPrice(convertPrice(startingBid, 'USD'), currency)}
                                                        collection={createListCollection({
                                                            items: bidOptions.filter(amount => amount > currentBid).map(amount => ({
                                                                label: formatPrice(convertPrice(amount, 'USD'), currency),
                                                                value: amount.toString() // Keep USD value for backend
                                                            })),
                                                        })}
                                                        onValueChange={(value) => {
                                                            const newStartingBid = value.value?.[0] ? parseFloat(value.value[0]) : startingBid;
                                                            setStartingBid(newStartingBid);
                                                            // Auto-adjust max budget if starting bid is higher
                                                            if (newStartingBid >= maxBudget) {
                                                                setMaxBudget(newStartingBid + 100);
                                                            }
                                                        }}
                                                    />
                                                    <Text fontSize="xs" color="gray.500" mt={1}>
                                                        Auto-bid will start placing bids from this amount
                                                    </Text>
                                                </Box>

                                                <Box>
                                                    <Text fontSize="xs" color="gray.600" mb={1}>{t('Auction.maxBudget')}</Text>
                                                    <ChakraSelect
                                                        size='md'
                                                        placeholder={formatPrice(convertPrice(maxBudget, 'USD'), currency)}
                                                        collection={createListCollection({
                                                            items: generateBidOptions().filter(amount => amount > startingBid).map(amount => ({
                                                                label: formatPrice(convertPrice(amount, 'USD'), currency),
                                                                value: amount.toString() // Keep USD value for backend
                                                            })),
                                                        })}
                                                        onValueChange={(value) => {
                                                            setMaxBudget(value.value?.[0] ? parseFloat(value.value[0]) : maxBudget);
                                                        }}
                                                    />
                                                </Box>

                                                <Box>
                                                    <Text fontSize="xs" color="gray.600" mb={1}>{t('Auction.bidIncrementAmount')}</Text>
                                                    <ChakraSelect
                                                        size='md'
                                                        placeholder={formatPrice(convertPrice(bidIncrement, 'USD'), currency)}
                                                        collection={createListCollection({
                                                            items: [1, 5, 10, 25, 50, 100].map(amount => ({
                                                                label: formatPrice(convertPrice(amount, 'USD'), currency),
                                                                value: amount.toString() // Keep USD value for backend
                                                            })),
                                                        })}
                                                        onValueChange={(value) => {
                                                            setBidIncrement(value.value?.[0] ? parseFloat(value.value[0]) : bidIncrement);
                                                        }}
                                                    />
                                                </Box>

                                                <Button
                                                    size='lg'
                                                    colorScheme="green"
                                                    onClick={handleEnableAutoBid}
                                                    loading={autoBidMutation.isPending}
                                                    disabled={autoBidMutation.isPending || maxBudget <= currentBid}
                                                    borderRadius='full'
                                                >
                                                    {t('Auction.enableAutoBid')}
                                                </Button>
                                            </VStack>
                                        )}

                                        {/* Disable Auto-bid */}
                                        {autoBidEnabled && (
                                            <Button
                                                size='md'
                                                variant="outline"
                                                colorScheme="red"
                                                onClick={handleDisableAutoBid}
                                                loading={cancelAutoBidMutation.isPending}
                                                disabled={cancelAutoBidMutation.isPending}
                                                borderRadius='full'
                                            >
                                                {t('Auction.disableAutoBid')}
                                            </Button>
                                        )}
                                    </VStack>
                                ) : (
                                    <Button
                                        size='lg'
                                        colorScheme="green"
                                        w="full"
                                        borderRadius='full'
                                        onClick={() => router.push('/auth/login')}
                                    >
                                        Login to Enable Auto-bid
                                    </Button>
                                )}
                            </Box>
                        )}
                    </VStack>
                )}

                {/* Auction Ended Message */}
                {auctionStatus === 'ended' && (
                    <Box p={4} bg="gray.100" borderRadius="md" textAlign="center">
                        <Text fontWeight="extrabold" color="gray.800">
                            Auction has ended
                        </Text>
                        {
                            bidHistory && bidHistory.bids.length > 0 ? (
                                <Text fontSize="md" color="gray.700" fontWeight="semibold" >
                                    Final bid: <Box as="span" me={2} fontSize={24} fontWeight="bold" color="green.600">{formatPrice(convertPrice(currentBid, 'USD'), currency)}</Box>
                                </Text>
                            ) : (
                                <Text fontSize="md" color="red.600">
                                    No bids were placed
                                </Text>
                            )
                        }
                        {userBid?.isWinning && (
                            <Text fontSize="sm" color="green.600" fontWeight="bold" mt={2}>
                                🎉 Congratulations! You won this auction!
                            </Text>
                        )}
                    </Box>
                )}

                {auctionStatus === 'upcoming' && (
                    <Box p={4} bg="blue.50" borderRadius="md" textAlign="center">
                        <Text fontWeight="bold" color="blue.700">
                            Auction hasn't started yet
                        </Text>
                        <Text fontSize="sm" color="gray.800">
                            {t('Product.startingBid')}: {formatPrice(convertPrice(startingPrice, 'USD'), currency)}
                        </Text>
                    </Box>
                )}

                <HStack gap={2} mt={4}>
                    {/* <Button
                        variant="ghost"
                        size="sm"
                        onClick={handleAddToWishlist}
                        color={isInWishlist ? "red.500" : "gray.600"}
                        _hover={{
                            color: isInWishlist ? "red.600" : "red.500",
                            bg: isInWishlist ? "red.50" : "gray.50"
                        }}
                        flex={1}
                    >
                        <FaHeart />
                        {isInWishlist ? 'Remove from Watchlist' : 'Add to Watchlist'}
                    </Button> */}
                </HStack>

                {/* Auction Info */}
                {/* <Box pt={4} borderTop="1px solid" borderColor="gray.100">
                    <Text fontSize="xs" color="gray.500">
                        • Bid increments: {formatUSD(minBidIncrement)} minimum
                    </Text>
                    <Text fontSize="xs" color="gray.500">
                        • Winner pays within 48 hours
                    </Text>
                    <Text fontSize="xs" color="gray.500">
                        • Secure payment processing
                    </Text>
                </Box> */}
            </VStack>
            {/* <BidHistoryModal
                isOpen={true}
                onClose={() => setBidHistoryOpen({
                    open: false, 
                    product: undefined
                })}
                productId={bidHistoryOpen.product.id}
                productName={bidHistoryOpen.product?.itemName}
            /> */}
        </Box>
    );
};

export default AuctionInfo;
