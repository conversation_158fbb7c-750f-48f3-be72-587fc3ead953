'use client'
import React, { useState } from 'react'
import {
    Box,
    Button,
    Heading,
    Text,
    VStack,
    HStack,
    Image,
    Badge,
    Icon,
    Flex,
} from '@chakra-ui/react'
import {
    DialogRoot,
    DialogTrigger,
    DialogContent,
    DialogHeader,
    DialogBody,
    DialogFooter,
    DialogTitle,
    DialogCloseTrigger,
} from '@/components/ui/dialog'
import {
    FaTrophy,
    FaCreditCard,
    FaGavel,
    FaClock,
    FaCheckCircle,
    FaExclamationTriangle,
    FaExternalLinkAlt,
    FaTimes,
    FaBell,
    FaCalendarAlt,
    FaDollarSign,
} from 'react-icons/fa'
import { formatUSD } from '@/utils/helpers/helper'
import { formatDistanceToNow, format, addDays } from 'date-fns'
import { useRouter } from 'next/navigation'
import { toaster } from '@/components/ui/toaster'

export interface AuctionWinnerNotificationData {
    id: string;
    productId: string;
    productName: string;
    productSlug?: string;
    productImage?: string;
    winningBid: number;
    auctionEndDate: string;
    paymentDeadline: string;
    checkoutUrl: string;
    isRead: boolean;
    createdAt: string;
}

interface AuctionWinnerNotificationProps {
    notification: AuctionWinnerNotificationData;
    onMarkAsRead?: (notificationId: string) => void;
    onDismiss?: (notificationId: string) => void;
    variant?: 'banner' | 'card' | 'modal' | 'toast';
    showActions?: boolean;
}

const AuctionWinnerNotification: React.FC<AuctionWinnerNotificationProps> = ({
    notification,
    onMarkAsRead,
    onDismiss,
    variant = 'card',
    showActions = true,
}) => {
    const router = useRouter();
    const [isDismissed, setIsDismissed] = useState(false);
    const [isDialogOpen, setIsDialogOpen] = useState(false);

    const handlePayNow = () => {
        router.push(notification.checkoutUrl);
        onMarkAsRead?.(notification.id);
        
        toaster.create({
            title: "Redirecting to Checkout",
            description: "Taking you to complete your purchase...",
            type: "info",
            duration: 2000,
        });
    };

    const handleViewAuction = () => {
        const url = notification.productSlug 
            ? `/auction/${notification.productSlug}` 
            : `/auction/product/${notification.productId}`;
        router.push(url);
        onMarkAsRead?.(notification.id);
    };

    const handleDismiss = () => {
        setIsDismissed(true);
        onDismiss?.(notification.id);
    };

    const getTimeRemaining = () => {
        const deadline = new Date(notification.paymentDeadline);
        const now = new Date();
        const diff = deadline.getTime() - now.getTime();
        
        if (diff <= 0) return { text: 'Expired', isUrgent: true, color: 'red' };
        
        const hours = Math.floor(diff / (1000 * 60 * 60));
        const days = Math.floor(hours / 24);
        
        if (hours <= 24) {
            return { 
                text: `${hours} hours left`, 
                isUrgent: true, 
                color: 'orange' 
            };
        }
        
        return { 
            text: `${days} days left`, 
            isUrgent: false, 
            color: 'blue' 
        };
    };

    const timeRemaining = getTimeRemaining();

    if (isDismissed) return null;

    // Banner variant - for top of page notifications
    if (variant === 'banner') {
        return (
            <Box
                w="full"
                bg="green.50"
                border="1px solid"
                borderColor="green.200"
                borderRadius="lg"
                p={4}
                position="relative"
            >
                <HStack justify="space-between" align="center" wrap="wrap" gap={4}>
                    <HStack gap={3}>
                        <Icon color="green.500" fontSize="xl">
                            <FaTrophy />
                        </Icon>
                        <VStack align="start" gap={0}>
                            <Text fontWeight="bold" color="green.700">
                                🎉 You won "{notification.productName}"!
                            </Text>
                            <Text fontSize="sm" color="green.600">
                                Complete your payment within {timeRemaining.text}
                            </Text>
                        </VStack>
                    </HStack>
                    
                    <HStack gap={2}>
                        <Button
                            colorPalette="green"
                            size="sm"
                            onClick={handlePayNow}
                        >
                            Pay Now
                        </Button>
                        <Button
                            variant="ghost"
                            size="sm"
                            onClick={handleDismiss}
                        >
                            <Icon>
                                <FaTimes />
                            </Icon>
                        </Button>
                    </HStack>
                </HStack>
            </Box>
        );
    }

    // Modal variant
    if (variant === 'modal') {
        return (
            <DialogRoot open={isDialogOpen} onOpenChange={setIsDialogOpen}>
                <DialogTrigger asChild>
                    <Button variant="ghost" size="sm">
                        <Icon>
                            <FaBell />
                        </Icon>
                    </Button>
                </DialogTrigger>

                <DialogContent>
                    <DialogHeader>
                        <DialogTitle>
                            <HStack gap={2}>
                                <Icon color="green.500">
                                    <FaTrophy />
                                </Icon>
                                <Text>Auction Won!</Text>
                            </HStack>
                        </DialogTitle>
                    </DialogHeader>

                    <DialogBody>
                        <VStack gap={4} align="stretch">
                            <Box textAlign="center">
                                <Text fontSize="lg" fontWeight="bold" color="green.600" mb={2}>
                                    🎉 Congratulations!
                                </Text>
                                <Text color="gray.600">
                                    You won the auction for "{notification.productName}"
                                </Text>
                            </Box>

                            {notification.productImage && (
                                <Image
                                    src={notification.productImage}
                                    alt={notification.productName}
                                    maxH="200px"
                                    objectFit="cover"
                                    borderRadius="md"
                                    mx="auto"
                                />
                            )}

                            <VStack gap={3}>
                                <HStack justify="space-between" w="full">
                                    <Text color="gray.600">Winning Bid:</Text>
                                    <Text fontWeight="bold" fontSize="lg" color="green.600">
                                        {formatUSD(notification.winningBid)}
                                    </Text>
                                </HStack>

                                <HStack justify="space-between" w="full">
                                    <Text color="gray.600">Payment Deadline:</Text>
                                    <Badge colorPalette={timeRemaining.color} variant="subtle">
                                        {timeRemaining.text}
                                    </Badge>
                                </HStack>
                            </VStack>

                            {timeRemaining.isUrgent && (
                                <Box p={3} bg="orange.50" border="1px solid" borderColor="orange.200" borderRadius="md">
                                    <HStack gap={2}>
                                        <Icon color="orange.500">
                                            <FaExclamationTriangle />
                                        </Icon>
                                        <Text fontSize="sm" color="orange.700">
                                            Payment deadline is approaching! Complete your purchase soon.
                                        </Text>
                                    </HStack>
                                </Box>
                            )}
                        </VStack>
                    </DialogBody>

                    <DialogFooter>
                        <HStack gap={3}>
                            <Button variant="outline" onClick={handleViewAuction}>
                                View Auction
                            </Button>
                            <Button colorPalette="green" onClick={handlePayNow}>
                                <HStack gap={2}>
                                    <Icon>
                                        <FaCreditCard />
                                    </Icon>
                                    <Text>Pay Now</Text>
                                </HStack>
                            </Button>
                        </HStack>
                    </DialogFooter>

                    <DialogCloseTrigger />
                </DialogContent>
            </DialogRoot>
        );
    }

    // Card variant - default
    return (
        <Box
            p={6}
            bg="white"
            borderRadius="xl"
            shadow="md"
            border="2px solid"
            borderColor="green.200"
            position="relative"
            transition="all 0.2s"
            _hover={{ shadow: "lg", transform: "translateY(-2px)" }}
        >
            {/* New notification indicator */}
            {!notification.isRead && (
                <Box
                    position="absolute"
                    top={-2}
                    right={-2}
                    w={4}
                    h={4}
                    bg="green.500"
                    borderRadius="full"
                    border="2px solid white"
                    animation="pulse 2s infinite"
                />
            )}

            <VStack gap={4} align="stretch">
                {/* Header */}
                <Flex justify="space-between" align="start">
                    <HStack gap={3}>
                        <Icon color="green.500" fontSize="2xl">
                            <FaTrophy />
                        </Icon>
                        <VStack align="start" gap={1}>
                            <Heading size="md" color="green.700">
                                🎉 Auction Won!
                            </Heading>
                            <Text fontSize="sm" color="gray.600">
                                {formatDistanceToNow(new Date(notification.createdAt), { addSuffix: true })}
                            </Text>
                        </VStack>
                    </HStack>
                    
                    {showActions && (
                        <Button
                            variant="ghost"
                            size="sm"
                            onClick={handleDismiss}
                        >
                            <Icon>
                                <FaTimes />
                            </Icon>
                        </Button>
                    )}
                </Flex>

                {/* Content */}
                <HStack gap={4} align="start">
                    {notification.productImage && (
                        <Image
                            src={notification.productImage}
                            alt={notification.productName}
                            boxSize="80px"
                            objectFit="cover"
                            borderRadius="md"
                        />
                    )}
                    
                    <VStack align="start" flex={1} gap={2}>
                        <Text fontWeight="semibold" color="gray.800">
                            {notification.productName}
                        </Text>
                        
                        <HStack gap={4} wrap="wrap">
                            <HStack gap={1}>
                                <Icon color="green.500" fontSize="sm">
                                    <FaDollarSign />
                                </Icon>
                                <Text fontSize="sm" color="gray.600">
                                    Winning bid: <strong>{formatUSD(notification.winningBid)}</strong>
                                </Text>
                            </HStack>
                            
                            <HStack gap={1}>
                                <Icon color={timeRemaining.color + '.500'} fontSize="sm">
                                    <FaClock />
                                </Icon>
                                <Text fontSize="sm" color={timeRemaining.color + '.600'}>
                                    {timeRemaining.text} to pay
                                </Text>
                            </HStack>
                        </HStack>
                    </VStack>
                </HStack>

                {/* Urgency warning */}
                {timeRemaining.isUrgent && (
                    <Box p={3} bg="orange.50" border="1px solid" borderColor="orange.200" borderRadius="md">
                        <HStack gap={2}>
                            <Icon color="orange.500">
                                <FaExclamationTriangle />
                            </Icon>
                            <Text fontSize="sm" color="orange.700" fontWeight="semibold">
                                Payment deadline is approaching! Complete your purchase soon.
                            </Text>
                        </HStack>
                    </Box>
                )}

                {/* Actions */}
                {showActions && (
                    <HStack gap={3} justify="end">
                        <Button
                            variant="outline"
                            onClick={handleViewAuction}
                        >
                            <HStack gap={2}>
                                <Icon>
                                    <FaExternalLinkAlt />
                                </Icon>
                                <Text>View Auction</Text>
                            </HStack>
                        </Button>
                        
                        <Button
                            colorPalette="green"
                            onClick={handlePayNow}
                        >
                            <HStack gap={2}>
                                <Icon>
                                    <FaCreditCard />
                                </Icon>
                                <Text>Pay Now</Text>
                            </HStack>
                        </Button>
                    </HStack>
                )}
            </VStack>
        </Box>
    );
};

export default AuctionWinnerNotification;
