"use client"
import React, { useRef, useState } from 'react'
import { Box, IconButton, Badge, useDisclosure, Icon, HoverCard, Portal, HStack, Text, VStack, Image, Button, Separator } from '@chakra-ui/react'
import { FaMinus, FaPlus, FaShoppingCart, FaTrash } from 'react-icons/fa'
import { CartItem, useCartQuery, useClearCartMutation, useRemoveFromCartMutation, useUpdateCartItemMutation } from '@/services/useCartQuery'
import { useRouter } from 'next/navigation'
import { IoCartOutline } from 'react-icons/io5'
import { useCurrencyLanguage } from '@/contexts/CurrencyLanguageContext'

const CartItemComponent: React.FC<{ item: CartItem }> = ({ item }) => {
    const updateCartItemMutation = useUpdateCartItemMutation();
    const removeFromCartMutation = useRemoveFromCartMutation();
    const { convertAndFormatPrice } = useCurrencyLanguage();

    const handleQuantityChange = async (newQuantity: number) => {
        if (newQuantity <= 0) {
            await removeFromCartMutation.mutateAsync(item.id);
        } else {
            await updateCartItemMutation.mutateAsync({
                itemId: item.id,
                data: { quantity: newQuantity }
            });
        }
    };

    const handleRemove = async () => {
        await removeFromCartMutation.mutateAsync(item.id);
    };

    const mainImage = item.product.images.find(img => img.isMain) || item.product.images[0];
    const itemTotal = item.price

    return (
        <Box p={4} borderWidth="1px" borderRadius="md" bg="gray.50">
            <HStack gap={4} align="start">
                <Box flexShrink={0}>
                    {mainImage ? (
                        <Image
                            src={mainImage.imageUrl}
                            alt={item.product.itemName}
                            boxSize="80px"
                            objectFit="cover"
                            borderRadius="md"
                        />
                    ) : (
                        <Box
                            boxSize="80px"
                            bg="gray.200"
                            borderRadius="md"
                            display="flex"
                            alignItems="center"
                            justifyContent="center"
                        >
                            <Text fontSize="xs" color="gray.500">No Image</Text>
                        </Box>
                    )}
                </Box>

                <VStack align="start" flex={1} gap={2}>
                    <Text fontWeight="bold" fontSize="sm" lineClamp={2}>
                        {item.product.itemName}
                    </Text>

                    <HStack justify="space-between" w="full">
                        <Text fontSize="sm" color="gray.600">
                            {convertAndFormatPrice(item.price, 'USD')} each
                        </Text>
                        <Text fontWeight="bold" color="green.600">
                            {convertAndFormatPrice(itemTotal, 'USD')}
                        </Text>
                    </HStack>

                    <IconButton
                        aria-label="Remove item"
                        size="xs"
                        colorScheme="red"
                        variant="ghost"
                        onClick={handleRemove}
                        disabled={removeFromCartMutation.isPending}
                    >
                        <FaTrash />
                    </IconButton>
                </VStack>
            </HStack>
        </Box>
    );
};

const CartIcon: React.FC = () => {
    const [isDrawerOpen, setIsDrawerOpen] = useState(false);
    const router = useRouter();
    const cartCardRef = useRef<HTMLDivElement>(null);

    const handleOpenCart = () => {
        setIsDrawerOpen(true);
    };

    const { data: cart, isLoading } = useCartQuery();
    const clearCartMutation = useClearCartMutation();
    const { convertAndFormatPrice } = useCurrencyLanguage();
    const itemCount = cart?.totalItems || 0;

    const handleClearCart = async () => {
        await clearCartMutation.mutateAsync();
    };

    const handleCheckout = () => {
        setIsDrawerOpen(false);
        router.push('/checkout')
    };


    return (
        <>
            <HoverCard.Root
                onOpenChange={(e) => setIsDrawerOpen(e.open)}
                size="sm"
                open={isDrawerOpen}>
                <HoverCard.Trigger asChild>
                    <Box
                        display={"flex"}
                        alignItems="center"
                        textStyle="sm"
                        fontWeight="semibold"
                        color="gray.700"
                        borderRadius="full"
                        px={4}
                        py={2}
                        _hover={{ bg: "gray.100" }}
                        _active={{ bg: "gray.200" }}
                        _focus={{ boxShadow: "outline" }}
                        cursor={"pointer"}
                        whiteSpace="nowrap"
                        fontSize={{
                            base: 10,
                            md: "sm"
                        }}
                        onClick={() => {
                            setIsDrawerOpen(!isDrawerOpen)
                        }}
                        position="relative"
                    >
                        <Icon
                            as={IoCartOutline}
                            boxSize={6}
                            color="gray.700"
                        />
                        {itemCount > 0 && (
                            <Badge
                                position="absolute"
                                top="-1"
                                right="-1"
                                colorScheme="red"
                                variant="solid"
                                borderRadius="full"
                                fontSize="xs"
                                minW="20px"
                                h="20px"
                                display="flex"
                                alignItems="center"
                                justifyContent="center"
                            >
                                {itemCount > 99 ? '99+' : itemCount}
                            </Badge>
                        )}
                    </Box>
                </HoverCard.Trigger>
                <Portal>
                    <HoverCard.Positioner placeContent={"center"}>
                        <HoverCard.Content p={2} minW={{
                            base: 200,
                            md: 400,
                        }} maxW={{
                            base: '95vw',
                            md: 500,
                        }} ref={cartCardRef}>
                            <HStack justify="space-between" px={2} pb={4} pt={2}>
                                <HStack>
                                    <FaShoppingCart />
                                    <Text>Shopping Cart</Text>
                                    {cart && cart.totalItems > 0 && (
                                        <Badge colorScheme="blue" variant="solid">
                                            {cart.totalItems}
                                        </Badge>
                                    )}
                                </HStack>
                            </HStack>
                            {isLoading ? (
                                <VStack gap={4}>
                                    <Text>Loading cart...</Text>
                                </VStack>
                            ) : !cart || cart.items.length === 0 ? (
                                <VStack gap={4} py={8} textAlign="center">
                                    <FaShoppingCart size={48} color="gray" />
                                    <Text fontSize="lg" color="gray.600">
                                        Your cart is empty
                                    </Text>
                                    <Text fontSize="sm" color="gray.500">
                                        Add some products to get started
                                    </Text>
                                </VStack>
                            ) : (
                                <VStack px={2} gap={4} align="stretch">
                                    {/* Cart Items */}
                                    <VStack gap={3} align="stretch">
                                        {cart.items.map((item) => (
                                            <CartItemComponent key={item.id} item={item} />
                                        ))}
                                    </VStack>

                                    {/* Clear Cart Button */}
                                    {cart.items.length > 0 && (
                                        <Button
                                            variant="ghost"
                                            colorScheme="red"
                                            size="sm"
                                            onClick={handleClearCart}
                                            loading={clearCartMutation.isPending}
                                            disabled={clearCartMutation.isPending}
                                        >
                                            Clear Cart
                                        </Button>
                                    )}
                                </VStack>
                            )}
                            {cart && cart.items.length > 0 && (
                                <VStack gap={4} w="full" p={2}>
                                    <Separator />

                                    {/* Cart Summary */}
                                    <HStack justify="space-between" w="full">
                                        <Text fontSize="lg" fontWeight="bold">
                                            Total ({cart.totalItems} items):
                                        </Text>
                                        <Text fontSize="xl" fontWeight="bold" color="green.600">
                                            {convertAndFormatPrice(cart.totalPrice, 'USD')}
                                        </Text>
                                    </HStack>

                                    {/* Checkout Button */}
                                    <Button
                                        borderRadius="full"
                                        colorScheme="blue"
                                        size="lg"
                                        w="full"
                                        onClick={handleCheckout}
                                    >
                                        Proceed to Checkout
                                    </Button>
                                </VStack>
                            )}
                        </HoverCard.Content>
                    </HoverCard.Positioner>
                </Portal>
            </HoverCard.Root>

            {/* <CartDrawer
                isOpen={isDrawerOpen}
                onClose={handleCloseCart}
                onCheckout={handleCheckout}
            /> */}
        </>
    );
};

export default CartIcon;
