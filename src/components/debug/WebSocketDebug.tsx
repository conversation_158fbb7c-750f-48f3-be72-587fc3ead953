'use client'

import React, { useState, useEffect } from 'react';
import { Box, Button, Text, VStack, HStack, Badge, Textarea } from '@chakra-ui/react';
import { useWebSocket } from '@/hooks/useWebSocket';

interface WebSocketDebugProps {
  productId?: string;
  userId?: string;
}

const WebSocketDebug: React.FC<WebSocketDebugProps> = ({ productId, userId }) => {
  const [messages, setMessages] = useState<any[]>([]);
  const [connectionStatus, setConnectionStatus] = useState<'connecting' | 'connected' | 'disconnected'>('connecting');
  const [testMessage, setTestMessage] = useState('');

  const { isConnected, sendMessage } = useWebSocket({
    productId,
    userId,
    onConnect: () => {
      console.log('WebSocket Debug: Connected');
      setConnectionStatus('connected');
      setMessages(prev => [...prev, { type: 'system', message: 'Connected to WebSocket', timestamp: new Date().toISOString() }]);
    },
    onDisconnect: () => {
      console.log('WebSocket Debug: Disconnected');
      setConnectionStatus('disconnected');
      setMessages(prev => [...prev, { type: 'system', message: 'Disconnected from WebSocket', timestamp: new Date().toISOString() }]);
    },
    onMessage: (message) => {
      console.log('WebSocket Debug: Message received', message);
      setMessages(prev => [...prev, { type: 'received', message: JSON.stringify(message, null, 2), timestamp: new Date().toISOString() }]);
    },
    onError: (error) => {
      console.error('WebSocket Debug: Error', error);
      setMessages(prev => [...prev, { type: 'error', message: `Error: ${error}`, timestamp: new Date().toISOString() }]);
    }
  });

  // Auto-connect on mount
  useEffect(() => {
    setMessages(prev => [...prev, { type: 'system', message: 'Attempting to connect to WebSocket...', timestamp: new Date().toISOString() }]);
  }, []);

  const sendTestMessage = () => {
    if (testMessage.trim()) {
      try {
        const message = JSON.parse(testMessage);
        sendMessage(message);
        setMessages(prev => [...prev, { type: 'sent', message: testMessage, timestamp: new Date().toISOString() }]);
        setTestMessage('');
      } catch (error) {
        setMessages(prev => [...prev, { type: 'error', message: `Invalid JSON: ${error}`, timestamp: new Date().toISOString() }]);
      }
    }
  };

  const clearMessages = () => {
    setMessages([]);
  };

  const subscribeToProduct = () => {
    if (productId) {
      sendMessage({
        type: 'subscribe',
        channel: `product:${productId}`
      });
    }
  };

  const subscribeToUser = () => {
    if (userId) {
      sendMessage({
        type: 'subscribe',
        channel: `user:${userId}`
      });
    }
  };

  return (
    <Box p={4} border="1px solid" borderColor="gray.200" borderRadius="md" bg="gray.50">
      <VStack align="stretch" gap={4}>
        <HStack justify="space-between">
          <Text fontSize="lg" fontWeight="bold">WebSocket Debug</Text>
          <Badge colorScheme={connectionStatus === 'connected' ? 'green' : connectionStatus === 'connecting' ? 'yellow' : 'red'}>
            {connectionStatus}
          </Badge>
        </HStack>

        <HStack gap={2}>
          <Button size="sm" onClick={subscribeToProduct} disabled={!productId || !isConnected}>
            Subscribe to Product
          </Button>
          <Button size="sm" onClick={subscribeToUser} disabled={!userId || !isConnected}>
            Subscribe to User
          </Button>
          <Button size="sm" onClick={clearMessages} variant="outline">
            Clear Messages
          </Button>
        </HStack>

        <VStack align="stretch" gap={2}>
          <Text fontSize="sm" fontWeight="medium">Send Test Message:</Text>
          <Textarea
            value={testMessage}
            onChange={(e) => setTestMessage(e.target.value)}
            placeholder='{"type": "subscribe", "channel": "product:123"}'
            size="sm"
            rows={3}
          />
          <Button size="sm" onClick={sendTestMessage} disabled={!isConnected}>
            Send Message
          </Button>
        </VStack>

        <Box>
          <Text fontSize="sm" fontWeight="medium" mb={2}>Messages ({messages.length}):</Text>
          <Box
            maxH="300px"
            overflowY="auto"
            border="1px solid"
            borderColor="gray.300"
            borderRadius="md"
            p={2}
            bg="white"
          >
            {messages.length === 0 ? (
              <Text fontSize="sm" color="gray.500">No messages yet...</Text>
            ) : (
              messages.map((msg, index) => (
                <Box key={index} mb={2} p={2} borderRadius="sm" bg={
                  msg.type === 'system' ? 'blue.50' :
                  msg.type === 'sent' ? 'green.50' :
                  msg.type === 'received' ? 'gray.50' :
                  'red.50'
                }>
                  <HStack justify="space-between" mb={1}>
                    <Badge size="sm" colorScheme={
                      msg.type === 'system' ? 'blue' :
                      msg.type === 'sent' ? 'green' :
                      msg.type === 'received' ? 'gray' :
                      'red'
                    }>
                      {msg.type}
                    </Badge>
                    <Text fontSize="xs" color="gray.500">
                      {new Date(msg.timestamp).toLocaleTimeString()}
                    </Text>
                  </HStack>
                  <Text fontSize="xs" fontFamily="mono" whiteSpace="pre-wrap">
                    {msg.message}
                  </Text>
                </Box>
              ))
            )}
          </Box>
        </Box>

        <Box fontSize="xs" color="gray.600">
          <Text>Product ID: {productId || 'Not set'}</Text>
          <Text>User ID: {userId || 'Not set'}</Text>
          <Text>WebSocket URL: {process.env.NEXT_PUBLIC_WS_URL}</Text>
        </Box>
      </VStack>
    </Box>
  );
};

export default WebSocketDebug;
