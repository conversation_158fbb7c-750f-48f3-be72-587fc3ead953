'use client'
import React from 'react'
import {
    Box,
    Button,
    Heading,
    Text,
    VStack,
    HStack,
    Image,
    Badge,
    Grid,
    Flex,
    Icon,
    Progress,
    SimpleGrid,
} from '@chakra-ui/react'
import {
    FaEye,
    FaGavel,
    FaClock,
    FaDollarSign,
    FaTrophy,
    FaTimesCircle,
    FaCheckCircle,
    FaSpinner,
    FaCreditCard,
    FaShippingFast,
    FaBoxOpen,
    FaExclamationTriangle,
    FaReceipt,
    FaArrowRight,
    FaCalendarAlt,
    FaInfoCircle,
    FaPhone,
    FaEnvelope,
} from 'react-icons/fa'
import { Tooltip } from '@/components/ui/tooltip'
import { formatDistanceToNow, format } from 'date-fns'
import { formatUSD } from '@/utils/helpers/helper'
import { 
    BiddingItemStatus, 
    getStatusColor, 
    getUrgencyColor, 
    formatTimeRemaining 
} from '@/services/useBiddingStatusQuery'

interface BiddingStatusCardProps {
    item: BiddingItemStatus;
    onPayNow?: (productId: string) => void;
    onViewOrder?: (orderId: string) => void;
    onViewAuction?: (productId: string, slug?: string) => void;
    onContactSupport?: () => void;
}

const BiddingStatusCard: React.FC<BiddingStatusCardProps> = ({
    item,
    onPayNow,
    onViewOrder,
    onViewAuction,
    onContactSupport,
}) => {
    const getStatusIcon = (status: string) => {
        switch (status) {
            case 'active': return FaSpinner;
            case 'won': return FaTrophy;
            case 'lost': return FaTimesCircle;
            case 'ended': return FaClock;
            case 'pending_payment': return FaCreditCard;
            case 'paid': return FaCheckCircle;
            case 'processing': return FaBoxOpen;
            case 'shipped': return FaShippingFast;
            case 'delivered': return FaTrophy;
            default: return FaInfoCircle;
        }
    };

    const getUrgencyIcon = (level: string) => {
        switch (level) {
            case 'critical': return FaExclamationTriangle;
            case 'high': return FaClock;
            case 'medium': return FaInfoCircle;
            default: return FaCheckCircle;
        }
    };

    const StatusIcon = getStatusIcon(item.orderStatus || item.biddingStatus);
    const UrgencyIcon = getUrgencyIcon(item.urgencyLevel);

    return (
        <Box
            p={6}
            bg="white"
            borderRadius="xl"
            shadow="md"
            border="1px solid"
            borderColor="gray.100"
            transition="all 0.2s"
            _hover={{ shadow: "lg", transform: "translateY(-2px)" }}
            position="relative"
        >
            {/* Urgency Indicator */}
            {item.isUrgent && (
                <Box
                    position="absolute"
                    top={-2}
                    right={-2}
                    w={4}
                    h={4}
                    bg={`${getUrgencyColor(item.urgencyLevel)}.500`}
                    borderRadius="full"
                    border="2px solid white"
                    animation="pulse 2s infinite"
                />
            )}

            <VStack gap={6} align="stretch">
                {/* Header */}
                <Flex justify="space-between" align="start" wrap="wrap" gap={4}>
                    <VStack align="start" gap={2}>
                        <Heading size="lg" color="gray.800">
                            {item.productName}
                        </Heading>
                        <HStack gap={2}>
                            <Icon color="gray.500">
                                <FaClock />
                            </Icon>
                            <Text color="gray.600" fontSize="sm">
                                Last bid {formatDistanceToNow(new Date(item.lastBidTime), { addSuffix: true })}
                            </Text>
                        </HStack>
                    </VStack>
                    
                    <VStack align="end" gap={2}>
                        <HStack gap={2}>
                            <Tooltip content={`Status: ${item.biddingStatus}`}>
                                <Badge 
                                    colorPalette={getStatusColor(item.biddingStatus)} 
                                    variant="subtle" 
                                    px={3} 
                                    py={1} 
                                    borderRadius="full"
                                >
                                    <HStack gap={1.5}>
                                        <Icon>
                                            <StatusIcon />
                                        </Icon>
                                        <Text textTransform="capitalize">{item.biddingStatus}</Text>
                                    </HStack>
                                </Badge>
                            </Tooltip>
                            
                            {item.orderStatus && (
                                <Tooltip content={`Order Status: ${item.orderStatus}`}>
                                    <Badge 
                                        colorPalette={getStatusColor(item.orderStatus)} 
                                        variant="solid" 
                                        px={3} 
                                        py={1} 
                                        borderRadius="full"
                                    >
                                        <HStack gap={1.5}>
                                            <Icon>
                                                <getStatusIcon(item.orderStatus) />
                                            </Icon>
                                            <Text textTransform="capitalize">{item.orderStatus.replace('_', ' ')}</Text>
                                        </HStack>
                                    </Badge>
                                </Tooltip>
                            )}
                        </HStack>
                        
                        {item.isUrgent && (
                            <Tooltip content={item.urgencyMessage}>
                                <Badge 
                                    colorPalette={getUrgencyColor(item.urgencyLevel)} 
                                    variant="outline" 
                                    px={2} 
                                    py={1} 
                                    borderRadius="full"
                                >
                                    <HStack gap={1}>
                                        <Icon>
                                            <UrgencyIcon />
                                        </Icon>
                                        <Text fontSize="xs" textTransform="uppercase">{item.urgencyLevel}</Text>
                                    </HStack>
                                </Badge>
                            </Tooltip>
                        )}
                    </VStack>
                </Flex>

                {/* Urgency Message */}
                {item.isUrgent && item.urgencyMessage && (
                    <Box 
                        p={3} 
                        bg={`${getUrgencyColor(item.urgencyLevel)}.50`} 
                        border="1px solid" 
                        borderColor={`${getUrgencyColor(item.urgencyLevel)}.200`} 
                        borderRadius="md"
                    >
                        <HStack gap={2}>
                            <Icon color={`${getUrgencyColor(item.urgencyLevel)}.500`}>
                                <UrgencyIcon />
                            </Icon>
                            <Text fontSize="sm" color={`${getUrgencyColor(item.urgencyLevel)}.700`} fontWeight="semibold">
                                {item.urgencyMessage}
                            </Text>
                            {item.paymentDeadline && (
                                <Text fontSize="xs" color={`${getUrgencyColor(item.urgencyLevel)}.600`}>
                                    ({formatTimeRemaining(item.paymentDeadline)} remaining)
                                </Text>
                            )}
                        </HStack>
                    </Box>
                )}

                {/* Content Grid */}
                <Grid templateColumns={{ base: '1fr', md: '200px 1fr' }} gap={6}>
                    {/* Product Image */}
                    <Box position="relative">
                        <Image
                            src={item.productImage || '/placeholder.jpg'}
                            alt={item.productName}
                            w="full"
                            h="200px"
                            objectFit="cover"
                            borderRadius="lg"
                            shadow="sm"
                        />
                        {item.biddingStatus === 'won' && (
                            <Box
                                position="absolute"
                                top={2}
                                right={2}
                                bg="green.500"
                                color="white"
                                px={2}
                                py={1}
                                borderRadius="full"
                                fontSize="xs"
                                fontWeight="bold"
                            >
                                <HStack gap={1}>
                                    <Icon>
                                        <FaTrophy />
                                    </Icon>
                                    <Text>WON</Text>
                                </HStack>
                            </Box>
                        )}
                    </Box>

                    {/* Details */}
                    <VStack align="start" gap={4}>
                        {/* Bid Information */}
                        <SimpleGrid columns={{ base: 1, md: 3 }} gap={4} w="full">
                            <Box>
                                <Text fontSize="sm" color="gray.600" mb={1}>Your Highest Bid</Text>
                                <Text fontSize="xl" fontWeight="bold" color="blue.600">
                                    {formatUSD(item.highestBid)}
                                </Text>
                            </Box>
                            <Box>
                                <Text fontSize="sm" color="gray.600" mb={1}>Current Leading Bid</Text>
                                <Text fontSize="xl" fontWeight="bold" color="gray.800">
                                    {formatUSD(item.currentBid)}
                                </Text>
                            </Box>
                            <Box>
                                <Text fontSize="sm" color="gray.600" mb={1}>Total Participants</Text>
                                <Text fontSize="xl" fontWeight="bold" color="purple.600">
                                    {item.totalBids}
                                </Text>
                            </Box>
                        </SimpleGrid>

                        {/* Progress Bar */}
                        {item.progressPercentage > 0 && (
                            <Box w="full">
                                <HStack justify="space-between" mb={2}>
                                    <Text fontSize="sm" color="gray.600">Progress</Text>
                                    <Text fontSize="sm" color="gray.600">{item.progressPercentage}%</Text>
                                </HStack>
                                <Progress 
                                    value={item.progressPercentage} 
                                    colorPalette="blue" 
                                    size="sm" 
                                    borderRadius="full"
                                />
                            </Box>
                        )}

                        {/* Order Information */}
                        {item.orderNumber && (
                            <Box w="full" p={3} bg="gray.50" borderRadius="md">
                                <HStack justify="space-between" wrap="wrap" gap={2}>
                                    <VStack align="start" gap={1}>
                                        <Text fontSize="sm" color="gray.600">Order Number</Text>
                                        <Text fontSize="md" fontWeight="semibold" color="gray.800">
                                            #{item.orderNumber}
                                        </Text>
                                    </VStack>
                                    {item.trackingNumber && (
                                        <VStack align="end" gap={1}>
                                            <Text fontSize="sm" color="gray.600">Tracking</Text>
                                            <Text fontSize="md" fontWeight="semibold" color="blue.600">
                                                {item.trackingNumber}
                                            </Text>
                                        </VStack>
                                    )}
                                </HStack>
                            </Box>
                        )}
                    </VStack>
                </Grid>

                {/* Action Buttons */}
                <Flex justify="space-between" align="center" wrap="wrap" gap={3}>
                    <VStack align="start" gap={1}>
                        <Text fontSize="md" fontWeight="semibold" color="gray.700">
                            {item.biddingStatus === 'won' && '🎉 Congratulations! You won this auction'}
                            {item.biddingStatus === 'lost' && '😔 Auction ended - You did not win'}
                            {item.biddingStatus === 'active' && item.isWinning && '🔥 You are currently winning!'}
                            {item.biddingStatus === 'active' && !item.isWinning && '⚡ You have been outbid'}
                            {item.biddingStatus === 'ended' && '⏰ Auction has ended'}
                        </Text>
                        {item.nextAction && (
                            <Text fontSize="sm" color="gray.600">
                                {item.nextAction.description}
                            </Text>
                        )}
                    </VStack>
                    
                    <HStack gap={3}>
                        {/* Next Action Button */}
                        {item.nextAction && (
                            <Button
                                colorPalette={item.nextAction.buttonColor}
                                size="lg"
                                variant={item.nextAction.isUrgent ? "solid" : "outline"}
                                onClick={() => {
                                    switch (item.nextAction?.type) {
                                        case 'pay':
                                            onPayNow?.(item.productId);
                                            break;
                                        case 'track':
                                            if (item.orderId) onViewOrder?.(item.orderId);
                                            break;
                                        case 'contact':
                                            onContactSupport?.();
                                            break;
                                        default:
                                            if (item.nextAction.url) {
                                                window.open(item.nextAction.url, '_blank');
                                            }
                                    }
                                }}
                            >
                                <HStack gap={2}>
                                    <Icon>
                                        {item.nextAction.type === 'pay' && <FaCreditCard />}
                                        {item.nextAction.type === 'track' && <FaReceipt />}
                                        {item.nextAction.type === 'contact' && <FaPhone />}
                                        {item.nextAction.type === 'wait' && <FaClock />}
                                        {item.nextAction.type === 'review' && <FaEye />}
                                    </Icon>
                                    <Text>{item.nextAction.buttonText}</Text>
                                </HStack>
                            </Button>
                        )}
                        
                        {/* View Order Button */}
                        {item.orderId && (
                            <Button
                                colorPalette="blue"
                                variant="outline"
                                onClick={() => onViewOrder?.(item.orderId!)}
                            >
                                <HStack gap={2}>
                                    <Icon>
                                        <FaReceipt />
                                    </Icon>
                                    <Text>View Order</Text>
                                </HStack>
                            </Button>
                        )}
                        
                        {/* View Auction Button */}
                        <Button
                            colorPalette="blue"
                            variant="outline"
                            onClick={() => onViewAuction?.(item.productId, item.productSlug)}
                        >
                            <HStack gap={2}>
                                <Icon>
                                    <FaEye />
                                </Icon>
                                <Text>View Auction</Text>
                            </HStack>
                        </Button>
                    </HStack>
                </Flex>
            </VStack>
        </Box>
    );
};

export default BiddingStatusCard;
