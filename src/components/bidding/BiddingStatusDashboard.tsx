'use client'
import React from 'react'
import {
    Box,
    Heading,
    Text,
    VStack,
    HStack,
    SimpleGrid,
    Icon,
    Progress,
    Badge,
    Flex,
} from '@chakra-ui/react'
import {
    FaChartLine,
    FaTrophy,
    FaFire,
    FaAward,
    FaDollarSign,
    FaExclamationTriangle,
    FaClock,
    FaShoppingCart,
    FaCheckCircle,
    FaPercent,
} from 'react-icons/fa'
import { Tooltip } from '@/components/ui/tooltip'
import { formatUSD } from '@/utils/helpers/helper'
import { BiddingStatusSummary } from '@/services/useBiddingStatusQuery'

interface BiddingStatusDashboardProps {
    summary: BiddingStatusSummary;
    isLoading?: boolean;
}

const BiddingStatusDashboard: React.FC<BiddingStatusDashboardProps> = ({
    summary,
    isLoading = false,
}) => {
    // Removed unused function for now

    const StatCard = ({ 
        title, 
        value, 
        subtitle, 
        icon, 
        color, 
        progress, 
        isUrgent = false,
        tooltip 
    }: {
        title: string;
        value: string | number;
        subtitle?: string;
        icon: any;
        color: string;
        progress?: number;
        isUrgent?: boolean;
        tooltip?: string;
    }) => (
        <Tooltip content={tooltip}>
            <Box
                p={6}
                bg="white"
                borderRadius="xl"
                shadow="md"
                border="1px solid"
                borderColor={isUrgent ? `${color}.200` : "gray.100"}
                position="relative"
                transition="all 0.2s"
                _hover={{ shadow: "md", transform: "translateY(-1px)" }}
            >
                {isUrgent && (
                    <Box
                        position="absolute"
                        top={-2}
                        right={-2}
                        w={3}
                        h={3}
                        bg={`${color}.500`}
                        borderRadius="full"
                        border="2px solid white"
                        animation="pulse 2s infinite"
                    />
                )}
                
                <VStack gap={4} align="stretch">
                    <HStack justify="space-between" align="start">
                        <VStack align="start" gap={1}>
                            <Text fontSize="sm" color="gray.600" fontWeight="medium">
                                {title}
                            </Text>
                            <Text fontSize="2xl" fontWeight="bold" color="gray.800">
                                {value}
                            </Text>
                            {subtitle && (
                                <Text fontSize="xs" color="gray.500">
                                    {subtitle}
                                </Text>
                            )}
                        </VStack>
                        <Box
                            p={3}
                            bg={`${color}.50`}
                            borderRadius="lg"
                            color={`${color}.500`}
                        >
                            <Icon fontSize="xl">
                                {React.createElement(icon)}
                            </Icon>
                        </Box>
                    </HStack>
                    
                    {progress !== undefined && (
                        <Box>
                            <HStack justify="space-between" mb={1}>
                                <Text fontSize="xs" color="gray.500">Progress</Text>
                                <Text fontSize="xs" color="gray.500">{progress}%</Text>
                            </HStack>
                            <Box
                                w="full"
                                h="2"
                                bg="gray.200"
                                borderRadius="full"
                                overflow="hidden"
                            >
                                <Box
                                    h="full"
                                    bg={`${color}.500`}
                                    borderRadius="full"
                                    width={`${progress}%`}
                                    transition="width 0.3s ease"
                                />
                            </Box>
                        </Box>
                    )}
                </VStack>
            </Box>
        </Tooltip>
    );

    if (isLoading) {
        return (
            <VStack gap={6} align="stretch">
                <Box>
                    <Heading size="lg" color="gray.800" mb={2}>Bidding Dashboard</Heading>
                    <Text color="gray.600">Loading your bidding statistics...</Text>
                </Box>
                <SimpleGrid columns={{ base: 2, md: 4, lg: 6 }} gap={4}>
                    {Array.from({ length: 6 }).map((_, index) => (
                        <Box
                            key={index}
                            p={6}
                            bg="gray.100"
                            borderRadius="xl"
                            h="120px"
                        />
                    ))}
                </SimpleGrid>
            </VStack>
        );
    }

    return (
        <VStack gap={6} align="stretch">
            {/* Header */}
            <Flex justify="space-between" align="center" wrap="wrap" gap={4}>
                <VStack align="start" gap={1}>
                    <Heading size="lg" color="gray.800">Bidding Dashboard</Heading>
                    <Text color="gray.600">
                        Overview of your auction activities and status
                    </Text>
                </VStack>
                
                {summary.urgentActions > 0 && (
                    <Badge 
                        colorPalette="red" 
                        variant="solid" 
                        px={3} 
                        py={1} 
                        borderRadius="full"
                        fontSize="sm"
                    >
                        <HStack gap={1}>
                            <Icon>
                                <FaExclamationTriangle />
                            </Icon>
                            <Text>{summary.urgentActions} Urgent Action{summary.urgentActions > 1 ? 's' : ''}</Text>
                        </HStack>
                    </Badge>
                )}
            </Flex>

            {/* Statistics Grid */}
            <SimpleGrid columns={{ base: 2, md: 3, lg: 6 }} gap={4}>
                <StatCard
                    title="Total Bids"
                    value={summary.totalBids}
                    subtitle="All time"
                    icon={FaChartLine}
                    color="blue"
                    tooltip="Total number of bids you've placed across all auctions"
                />
                
                <StatCard
                    title="Active Bids"
                    value={summary.activeBids}
                    subtitle="Ongoing auctions"
                    icon={FaFire}
                    color="orange"
                    progress={summary.totalBids > 0 ? (summary.activeBids / summary.totalBids) * 100 : 0}
                    tooltip="Number of auctions you're currently participating in"
                />
                
                <StatCard
                    title="Won Auctions"
                    value={summary.wonAuctions}
                    subtitle="Successful bids"
                    icon={FaTrophy}
                    color="green"
                    progress={summary.totalBids > 0 ? (summary.wonAuctions / summary.totalBids) * 100 : 0}
                    tooltip="Number of auctions you've won"
                />
                
                <StatCard
                    title="Pending Payments"
                    value={summary.pendingPayments}
                    subtitle="Awaiting payment"
                    icon={FaClock}
                    color="orange"
                    isUrgent={summary.pendingPayments > 0}
                    tooltip="Number of won auctions waiting for payment completion"
                />
                
                <StatCard
                    title="Active Orders"
                    value={summary.activeOrders}
                    subtitle="In progress"
                    icon={FaShoppingCart}
                    color="purple"
                    tooltip="Number of orders currently being processed or shipped"
                />
                
                <StatCard
                    title="Completed Orders"
                    value={summary.completedOrders}
                    subtitle="Delivered"
                    icon={FaCheckCircle}
                    color="green"
                    tooltip="Number of orders that have been successfully delivered"
                />
            </SimpleGrid>

            {/* Performance Metrics */}
            <SimpleGrid columns={{ base: 1, md: 3 }} gap={4}>
                <StatCard
                    title="Total Spent"
                    value={formatUSD(summary.totalSpent)}
                    subtitle="On won auctions"
                    icon={FaDollarSign}
                    color="green"
                    tooltip="Total amount spent on successfully won auctions"
                />
                
                <StatCard
                    title="Average Bid"
                    value={formatUSD(summary.averageBid)}
                    subtitle="Per auction"
                    icon={FaAward}
                    color="blue"
                    tooltip="Average amount you bid per auction"
                />
                
                <StatCard
                    title="Win Rate"
                    value={`${summary.winRate.toFixed(1)}%`}
                    subtitle="Success rate"
                    icon={FaPercent}
                    color="purple"
                    progress={summary.winRate}
                    tooltip="Percentage of auctions you've won out of total participated"
                />
            </SimpleGrid>

            {/* Quick Insights */}
            {(summary.urgentActions > 0 || summary.pendingPayments > 0) && (
                <Box p={4} bg="orange.50" border="1px solid" borderColor="orange.200" borderRadius="lg">
                    <VStack gap={3} align="stretch">
                        <HStack gap={2}>
                            <Icon color="orange.500">
                                <FaExclamationTriangle />
                            </Icon>
                            <Text fontSize="md" fontWeight="semibold" color="orange.700">
                                Action Required
                            </Text>
                        </HStack>
                        
                        <VStack gap={2} align="start">
                            {summary.pendingPayments > 0 && (
                                <Text fontSize="sm" color="orange.600">
                                    • You have {summary.pendingPayments} pending payment{summary.pendingPayments > 1 ? 's' : ''} for won auctions
                                </Text>
                            )}
                            {summary.urgentActions > summary.pendingPayments && (
                                <Text fontSize="sm" color="orange.600">
                                    • {summary.urgentActions - summary.pendingPayments} other urgent action{(summary.urgentActions - summary.pendingPayments) > 1 ? 's' : ''} require{(summary.urgentActions - summary.pendingPayments) === 1 ? 's' : ''} your attention
                                </Text>
                            )}
                        </VStack>
                    </VStack>
                </Box>
            )}
        </VStack>
    );
};

export default BiddingStatusDashboard;
