'use client'
import React, { useState } from 'react'
import {
  Box,
  Button,
  Heading,
  Text,
  VStack,
  HStack,
  Image,
  Badge,
  Grid,
  Skeleton,
  Flex,
  Icon,
  Container,
  Separator,
} from '@chakra-ui/react'

import {
  FaEye,
  FaShoppingBag,
  FaClock,
  FaDollarSign,
  FaSearch,
  FaTruck,
  FaCheckCircle,
  FaTimesCircle,
  FaSpinner
} from 'react-icons/fa'
import { useOrdersQuery, Order, OrdersQueryParams } from '@/services/useOrderQuery'
import { formatUSD } from '@/utils/helpers/helper'
import { formatDistanceToNow } from 'date-fns'
import { useRouter } from 'next/navigation'
import FormSelectField, { SelectOption } from '@/components/ui/form/FormSelectField'
import { SingleValue } from 'react-select'
import { useTranslations } from 'next-intl'
import { useSession } from 'next-auth/react'
import Link from 'next/link'

// Status options
const statusOptions: SelectOption[] = [
  { value: 'all', label: 'All Status' },
  { value: 'pending', label: 'Pending' },
  { value: 'processing', label: 'Processing' },
  { value: 'shipped', label: 'Shipped' },
  { value: 'delivered', label: 'Delivered' },
  { value: 'cancelled', label: 'Cancelled' },
]

const paymentStatusOptions: SelectOption[] = [
  { value: 'all', label: 'All Payment Status' },
  { value: 'pending', label: 'Pending Payment' },
  { value: 'paid', label: 'Paid' },
  { value: 'failed', label: 'Failed' },
  { value: 'refunded', label: 'Refunded' },
]

// Status badge component
const StatusBadge = ({ status }: { status: string }) => {
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return 'orange'
      case 'processing': return 'blue'
      case 'shipped': return 'purple'
      case 'delivered': return 'green'
      case 'cancelled': return 'red'
      default: return 'gray'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending': return FaClock
      case 'processing': return FaSpinner
      case 'shipped': return FaTruck
      case 'delivered': return FaCheckCircle
      case 'cancelled': return FaTimesCircle
      default: return FaClock
    }
  }

  return (
    <Badge colorScheme={getStatusColor(status)} variant="subtle">
      <HStack gap={1}>
        <Icon as={getStatusIcon(status)} boxSize={3} />
        <Text textTransform="capitalize">{status}</Text>
      </HStack>
    </Badge>
  )
}

// Payment status badge component
const PaymentStatusBadge = ({ status }: { status: string }) => {
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return 'orange'
      case 'paid': return 'green'
      case 'failed': return 'red'
      case 'refunded': return 'blue'
      default: return 'gray'
    }
  }

  return (
    <Badge colorScheme={getStatusColor(status)} variant="outline" size="sm">
      <Text textTransform="capitalize">{status}</Text>
    </Badge>
  )
}

const AccountBuyingPage = () => {
  const router = useRouter()
  const t = useTranslations()
  const { data: session } = useSession()
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState<string>('all')
  const [paymentStatusFilter, setPaymentStatusFilter] = useState<string>('all')
  const [currentPage, setCurrentPage] = useState(1)

  // Query parameters for user's orders
  const queryParams: OrdersQueryParams = {
    page: currentPage,
    limit: 10,
    status: statusFilter !== 'all' ? statusFilter as any : undefined,
    paymentStatus: paymentStatusFilter !== 'all' ? paymentStatusFilter as any : undefined,
    sortBy: 'createdAt',
    sortOrder: 'desc',
  }

  const { data: ordersData, isLoading, error } = useOrdersQuery(queryParams)

  const handleStatusChange = (option: SingleValue<SelectOption>) => {
    setStatusFilter(option?.value || 'all')
    setCurrentPage(1)
  }

  const handlePaymentStatusChange = (option: SingleValue<SelectOption>) => {
    setPaymentStatusFilter(option?.value || 'all')
    setCurrentPage(1)
  }

  const handleViewOrder = (orderId: string) => {
    router.push(`/order-tracking/${orderId}`)
  }

  if (!session) {
    return (
      <Container maxW="6xl" py={8}>
        <Text>Please login to view your orders.</Text>
      </Container>
    )
  }

  return (
    <Container maxW="6xl" py={8}>
      {/* Header */}
      <VStack align="start" gap={6} mb={8}>
        <VStack align="start" gap={1}>
          <Heading size="xl">My Orders</Heading>
          <Text color="gray.600">
            Track and manage your purchase orders
          </Text>
        </VStack>

        {/* Filters */}
        <Grid templateColumns={{ base: '1fr', md: '1fr 1fr 1fr' }} gap={4} w="full">
          <FormSelectField
            label="Order Status"
            options={statusOptions}
            value={statusOptions.find(opt => opt.value === statusFilter)}
            onChange={(selectedOption) => {
              const option = selectedOption as SingleValue<SelectOption>;
              handleStatusChange(option)
            }}
            placeholder="Select status..."
          />
          <FormSelectField
            label="Payment Status"
            options={paymentStatusOptions}
            value={paymentStatusOptions.find(opt => opt.value === paymentStatusFilter)}
            onChange={(selectedOption) => {
              const option = selectedOption as SingleValue<SelectOption>;
              handlePaymentStatusChange(option)
            }}
            placeholder="Select payment status..."
          />
        </Grid>
      </VStack>

      {/* Orders List */}
      {isLoading ? (
        <VStack gap={4} align="stretch">
          {Array.from({ length: 5 }).map((_, index) => (
            <Skeleton key={index} height="200px" />
          ))}
        </VStack>
      ) : error ? (
        <Box p={8} bg="white" borderRadius="lg" shadow="sm" border="1px" borderColor="gray.200">
          <VStack gap={4}>
            <Icon as={FaTimesCircle} boxSize={12} color="red.500" />
            <Text color="red.500">Failed to load orders</Text>
          </VStack>
        </Box>
      ) : !ordersData?.orders.length ? (
        <Box p={8} bg="white" borderRadius="lg" shadow="sm" border="1px" borderColor="gray.200">
          <VStack gap={4}>
            <Icon as={FaShoppingBag} boxSize={12} color="gray.400" />
            <Text color="gray.500">No orders found</Text>
            <Button colorScheme="blue" onClick={() => router.push('/marketplace')}>
              Start Shopping
            </Button>
          </VStack>
        </Box>
      ) : (
        <VStack gap={6} align="stretch">
          {ordersData.orders.map((order) => (
            <Box key={order.id} p={6} bg="white" borderRadius="lg" shadow="sm" border="1px" borderColor="gray.200">
              <VStack gap={4} align="stretch">
                {/* Order Header */}
                <Flex justify="space-between" align="start" wrap="wrap" gap={4}>
                  <VStack align="start" gap={1}>
                    <Text fontWeight="bold" fontSize="lg">
                      Order #{order.orderNumber}
                    </Text>
                    <Text color="gray.600" fontSize="sm">
                      {formatDistanceToNow(new Date(order.createdAt), { addSuffix: true })}
                    </Text>
                  </VStack>
                  <HStack gap={2}>
                    <StatusBadge status={order.status} />
                    <PaymentStatusBadge status={order.paymentStatus} />
                  </HStack>
                </Flex>

                <Separator />

                {/* Order Items */}
                <VStack gap={3} align="stretch">
                  {order.items.slice(0, 2).map((item) => (
                    <HStack key={item.id} gap={4}>
                      <Image
                        src={item.product.images.find(img => img.isMain)?.imageUrl || '/placeholder.jpg'}
                        alt={item.product.itemName}
                        boxSize="60px"
                        objectFit="cover"
                        borderRadius="md"
                      />
                      <VStack align="start" flex={1} gap={1}>
                        <Text fontWeight="medium" lineClamp={1}>
                          {item.product.itemName}
                        </Text>
                        <HStack gap={2}>
                          <Text fontSize="sm" color="gray.600">
                            Qty: {item.quantity}
                          </Text>
                          <Text fontSize="sm" fontWeight="medium">
                            {formatUSD(item.price)}
                          </Text>
                        </HStack>
                      </VStack>
                    </HStack>
                  ))}
                  {order.items.length > 2 && (
                    <Text fontSize="sm" color="gray.600">
                      +{order.items.length - 2} more items
                    </Text>
                  )}
                </VStack>

                <Separator />

                {/* Order Summary */}
                <Flex justify="space-between" align="center" wrap="wrap" gap={4}>
                  <VStack align="start" gap={1}>
                    <Text fontSize="sm" color="gray.600">Total Amount</Text>
                    <Text fontSize="xl" fontWeight="bold" color="gray.800">
                      {formatUSD(order.total)}
                    </Text>
                  </VStack>
                  <Button
                    colorScheme="blue"
                    variant="outline"
                    onClick={() => handleViewOrder(order.id)}
                  >
                    <FaEye />
                    View Details
                  </Button>
                </Flex>
              </VStack>
            </Box>
          ))}

          {/* Pagination */}
          {ordersData.pagination.totalPages > 1 && (
            <Flex justify="center" gap={2} mt={6}>
              <Button
                variant="outline"
                disabled={currentPage === 1}
                onClick={() => setCurrentPage(currentPage - 1)}
              >
                Previous
              </Button>
              <Text alignSelf="center" mx={4}>
                Page {currentPage} of {ordersData.pagination.totalPages}
              </Text>
              <Button
                variant="outline"
                disabled={currentPage === ordersData.pagination.totalPages}
                onClick={() => setCurrentPage(currentPage + 1)}
              >
                Next
              </Button>
            </Flex>
          )}
        </VStack>
      )}
    </Container>
  )
}

export default AccountBuyingPage
