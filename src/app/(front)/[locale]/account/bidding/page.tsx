'use client'
import React, { useState } from 'react'
import {
    Box,
    Button,
    Heading,
    Text,
    VStack,
    HStack,
    Image,
    Badge,
    Grid,
    Skeleton,
    Flex,
    Icon,
    Container,
    Separator,
    Card,
    CardBody,
    CardHeader,
    Progress,
    Alert,
    AlertIcon,
    AlertTitle,
    AlertDescription,
    Tooltip,
    SimpleGrid,
    Stat,
    StatLabel,
    StatNumber,
    StatHelpText,
    StatArrow,
    Divider,
    useColorModeValue,
} from '@chakra-ui/react'
import {
    FaEye,
    FaGavel,
    FaClock,
    FaDollarSign,
    FaSearch,
    FaTrophy,
    FaTimesCircle,
    FaCheckCircle,
    FaSpinner,
    FaCreditCard,
    FaShippingFast,
    FaBoxOpen,
    FaExclamationTriangle,
    FaChartLine,
    FaCalendarAlt,
    FaUsers,
    FaArrowUp,
    FaArrowDown,
    FaReceipt,
    FaMapMarkerAlt,
    FaPhone,
    FaEnvelope,
    FaHistory,
    FaInfoCircle,
    FaExternalLinkAlt,
    FaHeart,
    FaStar,
    FaFire,
    FaAward,
    FaHandshake,
    FaLock,
    FaUnlock,
} from 'react-icons/fa'
import { useUserBidsQuery, UserBidsQueryParams } from '@/services/useBiddingQuery'
import { formatUSD } from '@/utils/helpers/helper'
import { formatDistanceToNow, format, isAfter, isBefore, addDays } from 'date-fns'
import { useRouter } from 'next/navigation'
import FormSelectField, { SelectOption } from '@/components/ui/form/FormSelectField'
import { SingleValue } from 'react-select'
import { useTranslations } from 'next-intl'
import { useSession } from 'next-auth/react'
import Link from 'next/link'
import { toaster } from '@/components/ui/toaster'

// Status options
const statusOptions: SelectOption[] = [
    { value: 'all', label: 'All Status' },
    { value: 'active', label: 'Active Auctions' },
    { value: 'ended', label: 'Ended Auctions' },
    { value: 'won', label: 'Won Auctions' },
    { value: 'lost', label: 'Lost Auctions' },
]

// Enhanced Auction status badge component
const AuctionStatusBadge = ({ status }: { status: string }) => {
    const getStatusConfig = (status: string) => {
        switch (status) {
            case 'active':
                return {
                    color: 'blue',
                    icon: FaSpinner,
                    label: 'Active',
                    description: 'Auction is ongoing'
                }
            case 'ended':
                return {
                    color: 'gray',
                    icon: FaClock,
                    label: 'Ended',
                    description: 'Auction has ended'
                }
            case 'won':
                return {
                    color: 'green',
                    icon: FaTrophy,
                    label: 'Won',
                    description: 'You won this auction!'
                }
            case 'lost':
                return {
                    color: 'red',
                    icon: FaTimesCircle,
                    label: 'Lost',
                    description: 'You did not win this auction'
                }
            default:
                return {
                    color: 'gray',
                    icon: FaClock,
                    label: 'Unknown',
                    description: 'Status unknown'
                }
        }
    }

    const config = getStatusConfig(status)

    return (
        <Tooltip label={config.description} placement="top">
            <Badge
                colorScheme={config.color}
                variant="subtle"
                px={3}
                py={1}
                borderRadius="full"
                fontSize="xs"
                fontWeight="semibold"
            >
                <HStack gap={1.5}>
                    <Icon as={config.icon} boxSize={3} />
                    <Text>{config.label}</Text>
                </HStack>
            </Badge>
        </Tooltip>
    )
}

// Enhanced Winning status badge component
const WinningStatusBadge = ({ isWinning, auctionStatus }: { isWinning: boolean; auctionStatus: string }) => {
    if (auctionStatus === 'active') {
        return (
            <Tooltip label={isWinning ? 'You have the highest bid' : 'Someone has outbid you'} placement="top">
                <Badge
                    colorScheme={isWinning ? 'green' : 'orange'}
                    variant="outline"
                    size="sm"
                    px={2}
                    py={1}
                    borderRadius="full"
                    fontSize="xs"
                    fontWeight="semibold"
                >
                    <HStack gap={1}>
                        <Icon as={isWinning ? FaArrowUp : FaArrowDown} boxSize={2.5} />
                        <Text>{isWinning ? 'Leading' : 'Outbid'}</Text>
                    </HStack>
                </Badge>
            </Tooltip>
        )
    }
    return null
}

// Order Status Badge for won auctions
const OrderStatusBadge = ({ orderStatus }: { orderStatus?: string }) => {
    if (!orderStatus) return null

    const getOrderStatusConfig = (status: string) => {
        switch (status) {
            case 'pending_payment':
                return { color: 'orange', icon: FaCreditCard, label: 'Payment Pending' }
            case 'paid':
                return { color: 'blue', icon: FaCheckCircle, label: 'Payment Confirmed' }
            case 'processing':
                return { color: 'purple', icon: FaBoxOpen, label: 'Processing' }
            case 'shipped':
                return { color: 'cyan', icon: FaShippingFast, label: 'Shipped' }
            case 'delivered':
                return { color: 'green', icon: FaCheckCircle, label: 'Delivered' }
            case 'cancelled':
                return { color: 'red', icon: FaTimesCircle, label: 'Cancelled' }
            default:
                return { color: 'gray', icon: FaInfoCircle, label: status }
        }
    }

    const config = getOrderStatusConfig(orderStatus)

    return (
        <Badge
            colorScheme={config.color}
            variant="solid"
            px={2}
            py={1}
            borderRadius="full"
            fontSize="xs"
            fontWeight="semibold"
        >
            <HStack gap={1}>
                <Icon as={config.icon} boxSize={2.5} />
                <Text>{config.label}</Text>
            </HStack>
        </Badge>
    )
}

// Payment urgency indicator
const PaymentUrgencyIndicator = ({ auctionEndDate, orderStatus }: { auctionEndDate?: string; orderStatus?: string }) => {
    if (!auctionEndDate || orderStatus !== 'pending_payment') return null

    const endDate = new Date(auctionEndDate)
    const paymentDeadline = addDays(endDate, 3) // 3 days to pay
    const now = new Date()
    const hoursLeft = Math.max(0, Math.floor((paymentDeadline.getTime() - now.getTime()) / (1000 * 60 * 60)))

    if (hoursLeft <= 0) {
        return (
            <Alert status="error" borderRadius="md" size="sm">
                <AlertIcon />
                <AlertTitle fontSize="sm">Payment Overdue!</AlertTitle>
                <AlertDescription fontSize="xs">
                    Payment deadline has passed. Contact support.
                </AlertDescription>
            </Alert>
        )
    }

    if (hoursLeft <= 24) {
        return (
            <Alert status="warning" borderRadius="md" size="sm">
                <AlertIcon />
                <AlertTitle fontSize="sm">Payment Due Soon!</AlertTitle>
                <AlertDescription fontSize="xs">
                    Pay within {hoursLeft} hours to secure your item.
                </AlertDescription>
            </Alert>
        )
    }

    return null
}

const AccountBiddingPage = () => {
    const router = useRouter()
    const t = useTranslations()
    const { data: session } = useSession()
    const [statusFilter, setStatusFilter] = useState<string>('all')
    const [currentPage, setCurrentPage] = useState(1)

    // Color mode values
    const bgColor = useColorModeValue('gray.50', 'gray.900')
    const cardBg = useColorModeValue('white', 'gray.800')
    const borderColor = useColorModeValue('gray.200', 'gray.700')

    // Query parameters for user's bids
    const queryParams: UserBidsQueryParams = {
        page: currentPage,
        limit: 10,
        status: statusFilter !== 'all' ? statusFilter as any : undefined,
        sortBy: 'createdAt',
        sortOrder: 'desc',
    }

    const { data: bidsData, isLoading, error } = useUserBidsQuery(queryParams)

    const handleStatusChange = (option: SingleValue<SelectOption>) => {
        setStatusFilter(option?.value || 'all')
        setCurrentPage(1)
    }

    const handleViewBid = (productId: string, productSlug?: string) => {
        if (productSlug) {
            router.push(`/auction/${productSlug}`)
        } else {
            router.push(`/auction/product/${productId}`)
        }
    }

    const handlePayNow = (productId: string, bidId?: string) => {
        // Navigate to checkout for auction winner
        const checkoutUrl = `/checkout?type=bidding&productId=${productId}${bidId ? `&bidId=${bidId}` : ''}`
        router.push(checkoutUrl)
    }

    const handleViewOrder = (orderId: string) => {
        router.push(`/order-tracking/${orderId}`)
    }

    // Calculate statistics
    const stats = React.useMemo(() => {
        if (!bidsData?.bids) return null

        const totalBids = bidsData.bids.length
        const wonAuctions = bidsData.bids.filter(bid => bid.auctionStatus === 'won').length
        const activeAuctions = bidsData.bids.filter(bid => bid.auctionStatus === 'active').length
        const winRate = totalBids > 0 ? (wonAuctions / totalBids) * 100 : 0
        const totalSpent = bidsData.bids
            .filter(bid => bid.auctionStatus === 'won')
            .reduce((sum, bid) => sum + bid.highestBid, 0)

        return {
            totalBids,
            wonAuctions,
            activeAuctions,
            winRate,
            totalSpent
        }
    }, [bidsData?.bids])

    if (!session) {
        return (
            <Container maxW="6xl" py={8}>
                <Text>Please login to view your bidding history.</Text>
            </Container>
        )
    }

    return (
        <Container maxW="6xl" py={8}>
            {/* Header */}
            <VStack align="start" gap={6} mb={8}>
                <VStack align="start" gap={1}>
                    <Heading size="xl">My Bidding History</Heading>
                    <Text color="gray.600">
                        Track your auction bids and results
                    </Text>
                </VStack>

                {/* Filters */}
                <Grid templateColumns={{ base: '1fr', md: '1fr 2fr' }} gap={4} w="full">
                    <FormSelectField
                        label="Auction Status"
                        options={statusOptions}
                        value={statusOptions.find(opt => opt.value === statusFilter)}
                        onChange={(selectedOption) => {
                            const option = selectedOption as SingleValue<SelectOption>;
                            handleStatusChange(option)
                        }}
                        placeholder="Select status..."
                    />
                </Grid>
            </VStack>

            {/* Bidding List */}
            {isLoading ? (
                <VStack gap={4} align="stretch">
                    {Array.from({ length: 5 }).map((_, index) => (
                        <Skeleton key={index} height="200px" />
                    ))}
                </VStack>
            ) : error ? (
                <Box p={8} bg="white" borderRadius="lg" shadow="sm" border="1px" borderColor="gray.200">
                    <VStack gap={4}>
                        <Icon as={FaTimesCircle} boxSize={12} color="red.500" />
                        <Text color="red.500">Failed to load bidding history</Text>
                    </VStack>
                </Box>
            ) : !bidsData?.bids?.length ? (
                <Box p={8} bg="white" borderRadius="lg" shadow="sm" border="1px" borderColor="gray.200">
                    <VStack gap={4}>
                        <Icon as={FaGavel} boxSize={12} color="gray.400" />
                        <Text color="gray.500">No bidding history found</Text>
                        <Button colorScheme="blue" onClick={() => router.push('/marketplace?sellType=auction')}>
                            Browse Auctions
                        </Button>
                    </VStack>
                </Box>
            ) : (
                <VStack gap={6} align="stretch">
                    {bidsData?.bids?.map((bid) => (
                        <Box key={bid.productId} p={6} bg="white" borderRadius="lg" shadow="sm" border="1px" borderColor="gray.200">
                            <VStack gap={4} align="stretch">
                                {/* Bid Header */}
                                <Flex justify="space-between" align="start" wrap="wrap" gap={4}>
                                    <VStack align="start" gap={1}>
                                        <Text fontWeight="bold" fontSize="lg">
                                            {bid.product.itemName}
                                        </Text>
                                        <Text color="gray.600" fontSize="sm">
                                            Last bid {formatDistanceToNow(new Date(bid.lastBidTime), { addSuffix: true })}
                                        </Text>
                                    </VStack>
                                    <HStack gap={2}>
                                        <AuctionStatusBadge status={bid.auctionStatus} />
                                        <WinningStatusBadge isWinning={bid.isWinning} auctionStatus={bid.auctionStatus} />
                                    </HStack>
                                </Flex>

                                <Separator />

                                {/* Bid Content */}
                                <HStack gap={6} align="start">
                                    <Image
                                        src={bid.product.images.find((img: any) => img.isMain)?.imageUrl || '/placeholder.jpg'}
                                        alt={bid.product.itemName}
                                        boxSize="120px"
                                        objectFit="cover"
                                        borderRadius="md"
                                    />
                                    <VStack align="start" flex={1} gap={3}>
                                        <Grid templateColumns={{ base: '1fr', md: '1fr 1fr' }} gap={4} w="full">
                                            <VStack align="start" gap={2}>
                                                <Text fontSize="sm" color="gray.600">Your Highest Bid</Text>
                                                <Text fontSize="xl" fontWeight="bold" color="gray.800">
                                                    {formatUSD(bid.highestBid)}
                                                </Text>
                                            </VStack>
                                            <VStack align="start" gap={2}>
                                                <Text fontSize="sm" color="gray.600">Current Bid</Text>
                                                <Text fontSize="xl" fontWeight="bold">
                                                    {formatUSD(bid.product.currentBid || 0)}
                                                </Text>
                                            </VStack>
                                        </Grid>

                                        <HStack gap={4} wrap="wrap">
                                            <Text fontSize="sm" color="gray.600">
                                                Total Bids: {bid.totalBids}
                                            </Text>
                                            {bid.product.auctionEndDate && (
                                                <Text fontSize="sm" color="gray.600">
                                                    {bid.auctionStatus === 'active' ? 'Ends' : 'Ended'}: {' '}
                                                    {format(new Date(bid.product.auctionEndDate), 'MMM dd, yyyy HH:mm')}
                                                </Text>
                                            )}
                                        </HStack>
                                    </VStack>
                                </HStack>

                                <Separator />

                                {/* Actions */}
                                <Flex justify="space-between" align="center" wrap="wrap" gap={4}>
                                    <VStack align="start" gap={1}>
                                        <Text fontSize="sm" color="gray.600">
                                            {bid.auctionStatus === 'won' && 'Congratulations! You won this auction'}
                                            {bid.auctionStatus === 'lost' && 'Auction ended - You did not win'}
                                            {bid.auctionStatus === 'active' && bid.isWinning && 'You are currently winning!'}
                                            {bid.auctionStatus === 'active' && !bid.isWinning && 'You have been outbid'}
                                            {bid.auctionStatus === 'ended' && 'Auction has ended'}
                                        </Text>
                                    </VStack>
                                    <Button
                                        colorScheme="blue"
                                        variant="outline"
                                        onClick={() => handleViewBid(bid.productId, bid.product.slug)}
                                    >
                                        <HStack gap={2}>
                                            <Icon as={FaEye} />
                                            <Text>View Auction</Text>
                                        </HStack>
                                    </Button>
                                </Flex>
                            </VStack>
                        </Box>
                    ))}

                    {/* Pagination */}
                    {bidsData?.pagination && bidsData.pagination.totalPages > 1 && (
                        <Flex justify="center" gap={2} mt={6}>
                            <Button
                                variant="outline"
                                disabled={currentPage === 1}
                                onClick={() => setCurrentPage(currentPage - 1)}
                            >
                                Previous
                            </Button>
                            <Text alignSelf="center" mx={4}>
                                Page {currentPage} of {bidsData.pagination.totalPages}
                            </Text>
                            <Button
                                variant="outline"
                                disabled={currentPage === bidsData.pagination.totalPages}
                                onClick={() => setCurrentPage(currentPage + 1)}
                            >
                                Next
                            </Button>
                        </Flex>
                    )}
                </VStack>
            )}
        </Container>
    )
}

export default AccountBiddingPage
