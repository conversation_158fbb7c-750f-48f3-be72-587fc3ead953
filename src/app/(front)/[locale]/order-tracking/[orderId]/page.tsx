'use client'
import React, { useState } from 'react'
import { useParams } from 'next/navigation'
import {
  Box,
  Container,
  VStack,
  HStack,
  Text,
  Heading,
  Card,
  Badge,
  Grid,
  GridItem,
  Image,
  Button,
  Spinner,
  Icon,
  Separator,
  Alert,

  Flex,
  Link,
  Tooltip,
  Steps
} from '@chakra-ui/react'
import {
  FaCheckCircle,
  FaClock,
  FaShippingFast,
  FaBox,
  FaMapMarkerAlt,
  FaCreditCard,
  FaPhone,
  FaEnvelope,
  FaCopy,
  FaTruck,
  FaStore,
  FaCalendarAlt,
  FaInfoCircle,
  FaExternalLinkAlt,
  FaDownload,
  FaReceipt,
  FaUndo,
  FaExclamationTriangle,
  FaShieldAlt,
  FaHeadset,
  FaGift,
  FaStar,
  FaCheck,
  FaSyncAlt,
  FaArrowLeft
} from 'react-icons/fa'

import { useOrderTrackingQuery } from '@/services/useOrderQuery'
import { useCurrencyLanguage } from '@/contexts/CurrencyLanguageContext'
import { toaster } from '@/components/ui/toaster'
import { formatDistanceToNow, format } from 'date-fns'

const OrderTrackingPage = () => {
  const params = useParams()
  const orderId = params.orderId as string
  const { formatPrice } = useCurrencyLanguage()
  const [showFullTimeline, setShowFullTimeline] = useState(false)

  const { data: orderData, isLoading, error } = useOrderTrackingQuery(orderId)

  const copyToClipboard = (text: string, type: string) => {
    navigator.clipboard.writeText(text)
    toaster.create({
      title: "Copied!",
      description: `${type} copied to clipboard`,
      type: "success",
      duration: 2000,
    })
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending_payment': return 'orange'
      case 'paid': return 'blue'
      case 'seller_confirmed': return 'purple'
      case 'shipped': return 'cyan'
      case 'delivered': return 'green'
      case 'cancelled': return 'red'
      default: return 'gray'
    }
  }

  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'pending_payment': return 'Waiting for Payment'
      case 'paid': return 'Payment Confirmed'
      case 'seller_confirmed': return 'Seller Confirmed'
      case 'shipped': return 'Shipped'
      case 'delivered': return 'Delivered'
      case 'cancelled': return 'Cancelled'
      default: return status.replace('_', ' ').toUpperCase()
    }
  }

  const getStatusDescription = (status: string) => {
    switch (status) {
      case 'pending': return 'Your order has been placed and is being processed'
      case 'pending_payment': return 'Please complete your payment to proceed with the order'
      case 'paid': return 'Your payment has been confirmed successfully'
      case 'processing': return 'Your order is being prepared by our team'
      case 'seller_confirmed': return 'The seller has confirmed your order and is preparing for shipment'
      case 'shipped': return 'Your order is on its way to your delivery address'
      case 'delivered': return 'Your order has been successfully delivered'
      case 'cancelled': return 'This order has been cancelled'
      default: return 'Order status updated'
    }
  }

  const getStatusIcon = (status: string, isCompleted: boolean, isCurrent: boolean) => {
    const iconProps = {
      size: '20px',
      color: isCompleted ? 'white' : isCurrent ? 'blue.500' : 'gray.400'
    }

    switch (status) {
      case 'pending': return <FaClock {...iconProps} />
      case 'pending_payment': return <FaCreditCard {...iconProps} />
      case 'paid': return <FaCheckCircle {...iconProps} />
      case 'processing': return <FaSyncAlt {...iconProps} />
      case 'seller_confirmed': return <FaStore {...iconProps} />
      case 'shipped': return <FaTruck {...iconProps} />
      case 'delivered': return <FaGift {...iconProps} />
      default: return <FaClock {...iconProps} />
    }
  }

  if (isLoading) {
    return (
      <Container maxW="6xl" py={8}>
        <VStack gap={8} align="center">
          <Spinner size="xl" />
          <Text>Loading order tracking...</Text>
        </VStack>
      </Container>
    )
  }

  if (error || !orderData?.order) {
    return (
      <Container maxW="6xl" py={8}>
        <Box p={4} bg="red.50" borderRadius="md" border="1px" borderColor="red.200">
          <Text fontWeight="bold" color="red.700">Order Not Found</Text>
          <Text fontSize="sm" color="red.600">
            The order you're looking for doesn't exist or you don't have permission to view it.
          </Text>
        </Box>
      </Container>
    )
  }

  const order = orderData.order

  return (
    <Container maxW="6xl" py={8}>
      <VStack gap={4} align="stretch">
        <HStack alignItems="center" gap={4}>
          <Link href='/account/buying'>
            <Icon as={FaArrowLeft} boxSize={4} />
          </Link>
          <Text as="div" fontSize="sm" color="gray.800">
            Back to Orders
          </Text>
        </HStack>
        {/* Header Card - Clean and Modern */}
        <Box bg="white" borderRadius="xl" border="1px solid" borderColor="gray.100" p={6}>
          <VStack gap={4} align="stretch">
            {/* Order Info Header */}
            <HStack justify="space-between" align="start">
              <VStack align="start" gap={1}>
                <Heading size="xl" color="gray.900" fontWeight="600">
                  Order #{order.orderNumber}
                </Heading>
                <Text fontSize="sm" color="gray.500">
                  Placed on {format(new Date(order.createdAt), 'PPP')}
                </Text>
                {order.trackingNumber && (
                  <HStack gap={2} mt={1}>
                    <Text fontSize="sm" color="gray.600">Tracking:</Text>
                    <Text fontWeight="medium" fontSize="sm">{order.trackingNumber}</Text>
                    <Button
                      size="xs"
                      variant="ghost"
                      onClick={() => copyToClipboard(order.trackingNumber, "Tracking number")}
                      p={1}
                    >
                      <FaCopy size={12} />
                    </Button>
                  </HStack>
                )}
              </VStack>
              <VStack align="end" gap={2}>
                <Badge
                  colorScheme={getStatusColor(order.status)}
                  variant="solid"
                  fontSize="xs"
                  px={3}
                  py={2}
                  borderRadius="md"
                  textTransform="capitalize"
                  fontWeight="semibold"
                >
                  {getStatusLabel(order.status)}
                </Badge>
                <Text fontSize="sm" color="gray.600" fontWeight="medium">
                  {order.progress}% Complete
                </Text>
              </VStack>
            </HStack>

            <Steps.Root
              step={order.timeline.findIndex((item: any) => item.isCurrent) + 1}
              count={order.timeline.length}>
              <Steps.List>
                {order.timeline.map((item: any, index: number) => (
                  <Steps.Item key={index} index={index} title={item.label}>
                    <VStack>
                      <Steps.Indicator />
                      <Box>
                        <Steps.Title>{item.label}</Steps.Title>
                        {item.timestamp && (
                          <Text fontSize="xs" color="gray.500" fontWeight="medium">
                            {format(new Date(item.timestamp), 'MMM dd, yyyy HH:mm')}
                          </Text>
                        )}
                        {item.key === 'shipped' && item.isCompleted && order.trackingNumber && (
                          <Box mt={2} p={2} bg="blue.50" borderRadius="md">
                            <Text fontSize="xs" color="blue.700">
                              📦 Tracking: {order.trackingNumber}
                            </Text>
                          </Box>
                        )}

                        {item.key === 'delivered' && item.isCompleted && (
                          <Box mt={2} p={2} bg="green.50" borderRadius="md">
                            <Text fontSize="xs" color="green.700">
                              🎉 Order completed successfully!
                            </Text>
                          </Box>
                        )}
                        {item.isCurrent && !item.isCompleted && (
                          <Badge colorScheme="blue" size="sm">
                            Current Status
                          </Badge>
                        )}
                      </Box>
                    </VStack>
                    <Steps.Separator />
                  </Steps.Item>
                ))}
              </Steps.List>
            </Steps.Root>
          </VStack>
        </Box>

        <Grid templateColumns={{ base: '1fr', lg: '2fr 1fr' }} gap={4}>
          {/* Main Content */}
          <GridItem>
            <VStack gap={4} align="stretch">
              {/* Order Items */}
              <Box bg="white" borderRadius="xl" border="1px solid" borderColor="gray.100" p={6}>
                <HStack justify="space-between" align="center" mb={6}>
                  <Heading size="lg" color="gray.900" fontWeight="600">
                    Order Items ({order.items.length})
                  </Heading>
                  <Text fontSize="sm" color="gray.600" fontWeight="medium">
                    Total: {formatPrice(Number(order.total), order.currency)}
                  </Text>
                </HStack>
                <VStack gap={4} align="stretch">
                  {order.items.map((item: any, index: number) => (
                    <Box key={item.id}>
                      <HStack gap={4} align="start">
                        <Box position="relative">
                          <Image
                            src={item.product.images?.[0]?.imageUrl || '/placeholder-product.jpg'}
                            alt={item.product.itemName}
                            w="100px"
                            h="100px"
                            objectFit="cover"
                            borderRadius="lg"
                            border="1px solid"
                            borderColor="gray.200"
                          />
                          {item.quantity > 1 && (
                            <Badge
                              position="absolute"
                              top="-8px"
                              right="-8px"
                              colorScheme="blue"
                              borderRadius="full"
                              fontSize="xs"
                            >
                              {item.quantity}x
                            </Badge>
                          )}
                        </Box>
                        <VStack align="start" flex="1" gap={2}>
                          <Text fontWeight="bold" fontSize="md" lineHeight="1.3">
                            {item.product.itemName}
                          </Text>
                          <HStack gap={4}>
                            <Text fontSize="sm" color="gray.600">
                              Qty: {item.quantity}
                            </Text>
                            <Text fontSize="sm" color="gray.600">
                              Type: {item.product.sellType === 'auction' ? 'Auction' : 'Buy Now'}
                            </Text>
                          </HStack>
                          <HStack justify="space-between" w="full">
                            <Text fontWeight="bold" color="blue.600" fontSize="lg">
                              {formatPrice(Number(item.price), item.currency || 'USD')}
                            </Text>
                            <Button size="sm" variant="outline">
                              View Product
                            </Button>
                          </HStack>
                        </VStack>
                      </HStack>
                      {index < order.items.length - 1 && <Separator mt={4} />}
                    </Box>
                  ))}
                </VStack>
              </Box>
              {/* Order Summary */}
              <Box bg="white" borderRadius="xl" border="1px solid" borderColor="gray.100" p={6}>
                <HStack gap={2} mb={6}>
                  <FaReceipt color="gray.600" />
                  <Heading size="lg" color="gray.900" fontWeight="600">Order Summary</Heading>
                </HStack>
                <VStack gap={4} align="stretch">
                  <VStack gap={3} align="stretch">
                    <HStack justify="space-between">
                      <Text>Subtotal ({order.items.length} items):</Text>
                      <Text fontWeight="medium">
                        {formatPrice(Number(order.subtotal), order.currency)}
                      </Text>
                    </HStack>
                    <HStack justify="space-between">
                      <Text>Shipping Fee:</Text>
                      <Text fontWeight="medium" color={Number(order.shippingCost || 0) === 0 ? 'green.600' : 'inherit'}>
                        {Number(order.shippingCost || 0) === 0 ? 'FREE' : formatPrice(Number(order.shippingCost || 0), order.currency)}
                      </Text>
                    </HStack>
                    <HStack justify="space-between">
                      <Text>Tax & Fees:</Text>
                      <Text fontWeight="medium">
                        {formatPrice(Number(order.tax || 0), order.currency)}
                      </Text>
                    </HStack>
                    {order.discount && (
                      <HStack justify="space-between">
                        <Text color="green.600">Discount:</Text>
                        <Text fontWeight="medium" color="green.600">
                          -{formatPrice(Number(order.discount), order.currency)}
                        </Text>
                      </HStack>
                    )}
                  </VStack>
                  <Separator />
                  <HStack justify="space-between">
                    <Text fontSize="lg" fontWeight="bold">Total Paid:</Text>
                    <Text fontSize="lg" fontWeight="bold" color="blue.600">
                      {formatPrice(Number(order.total), order.currency)}
                    </Text>
                  </HStack>

                  {/* Payment Info */}
                  <Box bg="gray.50" p={3} borderRadius="md">
                    <VStack gap={2} align="stretch">
                      <HStack justify="space-between">
                        <Text fontSize="sm" color="gray.600">Payment Method:</Text>
                        <Text fontSize="sm" fontWeight="medium">
                          {order.paymentMethod?.replace('_', ' ').toUpperCase() || 'N/A'}
                        </Text>
                      </HStack>
                      <HStack justify="space-between">
                        <Text fontSize="sm" color="gray.600">Payment Status:</Text>
                        <Badge
                          bg={order.paymentStatus === 'paid' ? 'green' : 'orange'}
                          color={order.paymentStatus === 'paid' ? 'white' : 'black'}
                          size="md"
                          fontWeight="semibold"
                          px={3}
                        >
                          {order.paymentStatus?.toUpperCase()}
                        </Badge>
                      </HStack>
                    </VStack>
                  </Box>
                </VStack>
              </Box>

              {/* Order Details */}
              <Box bg="white" borderRadius="xl" border="1px solid" borderColor="gray.100" p={6}>
                <HStack gap={2} mb={6}>
                  <FaInfoCircle color="gray.600" />
                  <Heading size="lg" color="gray.900" fontWeight="600">Order Details</Heading>
                </HStack>
                <VStack gap={4} align="stretch">
                  <Grid templateColumns="repeat(2, 1fr)" gap={4}>
                    <VStack align="start" gap={2}>
                      <Text fontSize="sm" color="gray.600">Order Date</Text>
                      <Text fontWeight="medium">{format(new Date(order.createdAt), 'PPP')}</Text>
                    </VStack>
                    <VStack align="start" gap={2}>
                      <Text fontSize="sm" color="gray.600">Last Updated</Text>
                      <Text fontWeight="medium">{format(new Date(order.updatedAt), 'PPP')}</Text>
                    </VStack>
                    <VStack align="start" gap={2}>
                      <Text fontSize="sm" color="gray.600">Payment Method</Text>
                      <Text fontWeight="medium">{order.paymentMethod?.replace('_', ' ').toUpperCase() || 'N/A'}</Text>
                    </VStack>
                    <VStack align="start" gap={2}>
                      <Text fontSize="sm" color="gray.600">Currency</Text>
                      <Text fontWeight="medium">{order.currency}</Text>
                    </VStack>
                  </Grid>

                  {order.notes && (
                    <Box>
                      <Text fontSize="sm" color="gray.600" mb={2}>Order Notes</Text>
                      <Box p={3} bg="yellow.50" borderRadius="md" border="1px solid" borderColor="yellow.200">
                        <Text fontSize="sm" color="gray.700">{order.notes}</Text>
                      </Box>
                    </Box>
                  )}

                  {/* Order Progress Summary */}
                  <Box>
                    <Text fontSize="sm" color="gray.600" mb={3}>Progress Summary</Text>
                    <Box p={4} bg="gray.50" borderRadius="lg">
                      <VStack gap={3} align="stretch">
                        <HStack justify="space-between">
                          <Text fontSize="sm">Order Completion</Text>
                          <Text fontSize="sm" fontWeight="bold">{order.progress}%</Text>
                        </HStack>
                        <HStack justify="space-between">
                          <Text fontSize="sm">Estimated Total Time</Text>
                          <Text fontSize="sm" fontWeight="medium">
                            {order.status === 'delivered' ? 'Completed' : '3-7 business days'}
                          </Text>
                        </HStack>
                        <HStack justify="space-between">
                          <Text fontSize="sm">Next Action</Text>
                          <Text fontSize="sm" fontWeight="medium" color="blue.600">
                            {order.status === 'pending_payment' ? 'Complete Payment' :
                              order.status === 'paid' ? 'Seller Processing' :
                                order.status === 'processing' ? 'Preparing for Shipment' :
                                  order.status === 'seller_confirmed' ? 'Preparing for Shipment' :
                                    order.status === 'shipped' ? 'In Transit' :
                                      order.status === 'delivered' ? 'Order Complete' : 'Processing'}
                          </Text>
                        </HStack>
                      </VStack>
                    </Box>
                  </Box>
                </VStack>
              </Box>

              {/* Shipping Address */}
              {order.shippingAddress && (
                <Box bg="white" borderRadius="xl" border="1px solid" borderColor="gray.100" p={6}>
                  <HStack gap={2} mb={6}>
                    <FaMapMarkerAlt color="gray.600" />
                    <Heading size="lg" color="gray.900" fontWeight="600">Delivery Address</Heading>
                  </HStack>
                  <VStack align="start" gap={3}>
                    <Box>
                      <Text fontWeight="bold" fontSize="md">{order.shippingAddress.name}</Text>
                      <Text fontSize="sm" color="gray.600" mt={1}>
                        {order.shippingAddress.address}
                      </Text>
                      <Text fontSize="sm" color="gray.600">
                        {order.shippingAddress.city}, {order.shippingAddress.provinceRegion} {order.shippingAddress.zipCode}
                      </Text>
                      <Text fontSize="sm" color="gray.600">
                        {order.shippingAddress.country}
                      </Text>
                    </Box>

                    {/* Contact Info */}
                    <Box w="full">
                      <Separator mb={3} />
                      <VStack gap={2} align="start">
                        <Text fontSize="sm" fontWeight="medium" color="gray.700">
                          Contact Information
                        </Text>
                        <HStack gap={2}>
                          <FaPhone size="12px" color="gray" />
                          <Text fontSize="sm">+****************</Text>
                        </HStack>
                        <HStack gap={2}>
                          <FaEnvelope size="12px" color="gray" />
                          <Text fontSize="sm"><EMAIL></Text>
                        </HStack>
                      </VStack>
                    </Box>
                  </VStack>
                </Box>
              )}

              {/* Delivery Information */}
              {order.status === 'shipped' && (
                <Box bg="white" borderRadius="xl" border="1px solid" borderColor="gray.100" p={6}>
                  <HStack gap={2} mb={6}>
                    <FaTruck color="gray.600" />
                    <Heading size="lg" color="gray.900" fontWeight="600">Delivery Information</Heading>
                  </HStack>
                  <VStack gap={4} align="stretch">
                    {order.trackingNumber && (
                      <Box>
                        <Text fontSize="sm" fontWeight="medium" mb={3}>Tracking Details</Text>
                        <Box p={4} bg="blue.50" borderRadius="lg" border="1px solid" borderColor="blue.200">
                          <VStack gap={3} align="stretch">
                            <HStack justify="space-between">
                              <VStack align="start" gap={1}>
                                <Text fontSize="sm" color="gray.600">Tracking Number</Text>
                                <Text fontWeight="bold" fontSize="lg">{order.trackingNumber}</Text>
                              </VStack>
                              <Button size="sm" variant="outline" colorScheme="blue">
                                <FaExternalLinkAlt style={{ marginRight: '8px' }} />
                                Track Package
                              </Button>
                            </HStack>
                            <Separator />
                            <HStack justify="space-between">
                              <VStack align="start" gap={1}>
                                <Text fontSize="sm" color="gray.600">Courier</Text>
                                <Text fontWeight="medium">{order.courier || 'Standard Delivery'}</Text>
                              </VStack>
                              <VStack align="end" gap={1}>
                                <Text fontSize="sm" color="gray.600">Service Type</Text>
                                <Text fontWeight="medium">{order.serviceType || 'Regular'}</Text>
                              </VStack>
                            </HStack>
                          </VStack>
                        </Box>
                      </Box>
                    )}

                    {order.estimatedDelivery && (
                      <Box>
                        <Text fontSize="sm" fontWeight="medium" mb={3}>Estimated Delivery</Text>
                        <Box p={4} bg="green.50" borderRadius="lg" border="1px solid" borderColor="green.200">
                          <VStack gap={2} align="start">
                            <Text color="green.700" fontWeight="bold" fontSize="lg">
                              {format(new Date(order.estimatedDelivery), 'EEEE, MMMM dd, yyyy')}
                            </Text>
                            <Text fontSize="sm" color="green.600">
                              Between 9:00 AM - 6:00 PM
                            </Text>
                            <Text fontSize="xs" color="gray.600">
                              Delivery time may vary depending on location and weather conditions
                            </Text>
                          </VStack>
                        </Box>
                      </Box>
                    )}

                    {/* Delivery Instructions */}
                    <Box>
                      <Text fontSize="sm" fontWeight="medium" mb={3}>Delivery Instructions</Text>
                      <Box p={3} bg="gray.50" borderRadius="md">
                        <Text fontSize="sm" color="gray.700">
                          {order.deliveryInstructions || 'Please ensure someone is available to receive the package. If no one is available, the package will be left at a safe location or returned to the depot.'}
                        </Text>
                      </Box>
                    </Box>
                  </VStack>
                </Box>
              )}

            </VStack>
          </GridItem>

          {/* Sidebar */}
          <GridItem>
            <VStack gap={4} align="stretch">
              {/* Order Actions */}
              <Box bg="white" borderRadius="xl" border="1px solid" borderColor="gray.100" p={6}>
                <HStack gap={2} mb={6}>
                  <FaHeadset color="gray.600" />
                  <Heading size="lg" color="gray.900" fontWeight="600">Order Actions</Heading>
                </HStack>
                <VStack gap={3} align="stretch">
                  {/* Primary Actions */}
                  {order.status === 'pending_payment' && (
                    <Button
                      onClick={() => {
                        // Handle payment completion logic here
                        toaster.create({
                          title: "Payment",
                          description: "Redirecting to payment gateway...",
                          type: "info",
                          duration: 2000,
                        });
                        window.open(order?.payment.invoiceUrl, '_blank', 'width=800,height=600,scrollbars=yes,resizable=yes');
                      }}
                      colorScheme="blue" size="sm">
                      <FaCreditCard style={{ marginRight: '8px' }} />
                      Complete Payment
                    </Button>
                  )}

                  {order.status === 'delivered' && (
                    <Button colorScheme="green" variant="outline" size="sm">
                      <FaStar style={{ marginRight: '8px' }} />
                      Rate & Review
                    </Button>
                  )}

                  {/* Secondary Actions */}
                  <Button variant="outline" size="sm">
                    <FaDownload style={{ marginRight: '8px' }} />
                    Download Invoice
                  </Button>

                  <Button variant="outline" size="sm">
                    <FaEnvelope style={{ marginRight: '8px' }} />
                    Contact Seller
                  </Button>

                  <Separator />

                  {/* Support Actions */}
                  <Text fontSize="sm" fontWeight="medium" color="gray.700">
                    Need Help?
                  </Text>

                  <Button variant="ghost" size="sm" justifyContent="flex-start">
                    <FaHeadset style={{ marginRight: '8px' }} />
                    Live Chat Support
                  </Button>

                  <Button variant="ghost" size="sm" justifyContent="flex-start">
                    <FaPhone style={{ marginRight: '8px' }} />
                    Call Support: 1-800-HELP
                  </Button>

                  <Button variant="ghost" size="sm" justifyContent="flex-start">
                    <FaInfoCircle style={{ marginRight: '8px' }} />
                    Order FAQ
                  </Button>

                  {/* Conditional Actions */}
                  {(order.status === 'pending_payment' || order.status === 'paid') && (
                    <>
                      <Separator />
                      <Button
                        colorScheme="red"
                        variant="outline"
                        size="sm"
                        justifyContent="flex-start"
                      >
                        <FaUndo style={{ marginRight: '8px' }} />
                        Cancel Order
                      </Button>
                    </>
                  )}

                  {order.status === 'delivered' && (
                    <>
                      <Separator />
                      <Button
                        variant="outline"
                        size="sm"
                        justifyContent="flex-start"
                      >
                        <FaUndo style={{ marginRight: '8px' }} />
                        Return Item
                      </Button>
                    </>
                  )}
                </VStack>
              </Box>

              {/* Security & Trust */}
              {order.status !== 'pending_payment' && (
                <Box bg="white" borderRadius="xl" border="1px solid" borderColor="gray.100" p={6}>
                  <HStack gap={2} mb={6}>
                    <FaShieldAlt color="gray.600" />
                    <Heading size="lg" color="gray.900" fontWeight="600">Security & Protection</Heading>
                  </HStack>
                  <VStack gap={4} align="stretch">
                    <HStack gap={3}>
                      <Box
                        w="40px"
                        h="40px"
                        bg="green.100"
                        borderRadius="full"
                        display="flex"
                        alignItems="center"
                        justifyContent="center"
                      >
                        <FaShieldAlt color="green" size="18px" />
                      </Box>
                      <VStack align="start" gap={1}>
                        <Text fontSize="sm" fontWeight="medium">Buyer Protection</Text>
                        <Text fontSize="xs" color="gray.600">
                          Your purchase is protected by our guarantee. Get full refund if item doesn't match description.
                        </Text>
                      </VStack>
                    </HStack>

                    <HStack gap={3}>
                      <Box
                        w="40px"
                        h="40px"
                        bg="blue.100"
                        borderRadius="full"
                        display="flex"
                        alignItems="center"
                        justifyContent="center"
                      >
                        <FaCheck color="blue" size="18px" />
                      </Box>
                      <VStack align="start" gap={1}>
                        <Text fontSize="sm" fontWeight="medium">Secure Payment</Text>
                        <Text fontSize="xs" color="gray.600">
                          Your payment information is encrypted with industry-standard security protocols.
                        </Text>
                      </VStack>
                    </HStack>

                    <HStack gap={3}>
                      <Box
                        w="40px"
                        h="40px"
                        bg="purple.100"
                        borderRadius="full"
                        display="flex"
                        alignItems="center"
                        justifyContent="center"
                      >
                        <FaGift color="purple" size="18px" />
                      </Box>
                      <VStack align="start" gap={1}>
                        <Text fontSize="sm" fontWeight="medium">Return Policy</Text>
                        <Text fontSize="xs" color="gray.600">
                          30-day return policy for eligible items. Free return shipping for defective products.
                        </Text>
                      </VStack>
                    </HStack>

                    <Separator />

                    <Box>
                      <Text fontSize="sm" fontWeight="medium" mb={3}>Need Help?</Text>
                      <VStack gap={2} align="stretch">
                        <Button variant="outline" size="sm" justifyContent="flex-start">
                          <FaHeadset style={{ marginRight: '8px' }} />
                          24/7 Customer Support
                        </Button>
                        <Button variant="outline" size="sm" justifyContent="flex-start">
                          <FaInfoCircle style={{ marginRight: '8px' }} />
                          Order Tracking FAQ
                        </Button>
                        <Button variant="outline" size="sm" justifyContent="flex-start">
                          <FaExclamationTriangle style={{ marginRight: '8px' }} />
                          Report an Issue
                        </Button>
                      </VStack>
                    </Box>
                  </VStack>
                </Box>
              )}

              {/* Order Completion Section - Only show if delivered */}
              {order.status === 'delivered' && (
                <Card.Root>
                  <Card.Header>
                    <HStack gap={2}>
                      <FaStar />
                      <Heading size="md">Order Complete</Heading>
                    </HStack>
                  </Card.Header>
                  <Card.Body>
                    <VStack gap={4} align="stretch">
                      <Box textAlign="center" p={6} bg="green.50" borderRadius="lg">
                        <VStack gap={3}>
                          <Box
                            w="60px"
                            h="60px"
                            bg="green.500"
                            borderRadius="full"
                            display="flex"
                            alignItems="center"
                            justifyContent="center"
                          >
                            <FaCheck color="white" size="24px" />
                          </Box>
                          <Text fontSize="lg" fontWeight="bold" color="green.700">
                            Order Delivered Successfully!
                          </Text>
                          <Text fontSize="sm" color="green.600">
                            Thank you for your purchase. We hope you love your items!
                          </Text>
                        </VStack>
                      </Box>

                      <VStack gap={3} align="stretch">
                        <Text fontSize="md" fontWeight="medium">What's Next?</Text>

                        <Button colorScheme="blue" size="md">
                          <FaStar style={{ marginRight: '8px' }} />
                          Rate & Review Your Purchase
                        </Button>

                        <Button variant="outline" size="md">
                          <FaDownload style={{ marginRight: '8px' }} />
                          Download Receipt
                        </Button>

                        <Button variant="outline" size="md">
                          <FaGift style={{ marginRight: '8px' }} />
                          Shop Similar Items
                        </Button>
                      </VStack>

                      <Box p={3} bg="yellow.50" borderRadius="md" border="1px solid" borderColor="yellow.200">
                        <Text fontSize="sm" color="yellow.800" fontWeight="medium">
                          💡 Tip: You have 30 days to return or exchange this item if needed.
                        </Text>
                      </Box>
                    </VStack>
                  </Card.Body>
                </Card.Root>
              )}
            </VStack>
          </GridItem>
        </Grid>
      </VStack>
    </Container>
  )
}

export default OrderTrackingPage
