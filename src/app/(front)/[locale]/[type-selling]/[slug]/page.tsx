'use client'
import React, { memo, useCallback, useMemo, useState } from 'react';
import { Box, Heading, Text, VStack, HStack, Skeleton, Stack, Button } from '@chakra-ui/react';
import { FaHistory, FaChevronRight } from 'react-icons/fa';
import { usePara<PERSON>, useRouter } from 'next/navigation';
import ProductImageGallery from '@/components/product/ProductImageGallery';
import ProductDetailLayout from '@/components/product/ProductDetailLayout';
import AuctionInfo from '@/components/product/AuctionInfo';
import BuyNowInfo from '@/components/product/BuyNowInfo';
import SalesHistory from '@/components/product/SalesHistory';
import Breadcrumb from '@/components/ui/Breadcrumb';
// import WebSocketDebug from '@/components/debug/WebSocketDebug';
import { useProductBySlugQuery } from '@/services/useProductQuery';
import { useAddToCartMutation, useCartQuery } from '@/services/useCartQuery';
import { useBuyNowMutation } from '@/services/useCheckoutQuery';
import { formatDistanceToNow } from 'date-fns';
import ProductInfo from '@/components/product/ProductInfo';
import { useSession } from 'next-auth/react';
import BidHistoryModal from '@/components/product/BidHistoryModal';
import RelatedProducts from '@/components/product/RelatedProducts';

// Memoized loading skeleton component
const ProductDetailSkeleton = memo(() => (
    <Box>
        <ProductDetailLayout
            leftContent={
                <VStack gap={4} align="stretch">
                    <Box px={{ base: 0, md: 6 }}>
                        <Skeleton height="20px" width="300px" />
                    </Box>
                    <Box px={{ base: 0, md: 6 }}>
                        <Skeleton height={{ base: '300px', lg: '500px', xl: '650px' }} />
                    </Box>
                </VStack>
            }
            rightContent={
                <VStack gap={4} align="stretch">
                    <Skeleton height="40px" />
                    <Skeleton height="200px" />
                    <Skeleton height="100px" />
                </VStack>
            }
        />
    </Box>
));

ProductDetailSkeleton.displayName = 'ProductDetailSkeleton';

// Memoized error component
const ProductDetailError = memo(({ error }: { error: any }) => (
    <Box textAlign="center" py={20}>
        <Text fontSize="xl" color="red.500" mb={4}>
            {error instanceof Error ? error.message : 'Product not found'}
        </Text>
        <Text color="gray.600">
            The product you're looking for doesn't exist or has been removed.
        </Text>
    </Box>
));

ProductDetailError.displayName = 'ProductDetailError';

const ProductDetailPage = () => {
    const dataSession = useSession()
    const session = dataSession.data;
    const { slug } = useParams();
    const { data: cart, isLoading: isLoadingCart } = useCartQuery();
    const router = useRouter();

    // State for bid history modal
    const [isBidHistoryOpen, setIsBidHistoryOpen] = useState(false);

    const {
        data: product,
        isLoading,
        error,
        isError
    } = useProductBySlugQuery(slug as string);

    const addToCartMutation = useAddToCartMutation();
    const buyNowMutation = useBuyNowMutation();

    // Memoized breadcrumb items
    const breadcrumbItems = useMemo(() => [
        {
            label: 'Home',
            href: '/'
        },
        {
            label: 'Auction',
            href: '/auction'
        },
        {
            label: product?.itemName || 'Product',
            isCurrentPage: true
        }
    ], [product?.itemName]);

    // Memoized product transformation
    const transformedProduct = useMemo(() => {
        if (!product) return null;
        return {
            id: product.id,
            slug: product.slug || '',
            title: product.itemName,
            image: product.images.find(img => img.isMain)?.imageUrl || product.images[0]?.imageUrl || '',
            images: product.images.map(img => img.imageUrl),
            price: `$${product.priceUSD}`,
            bids: product.bidCount?.toString() || '0',
            type: product.sellType as 'auction' | 'buy-now',
            timeLeft: product.auctionEndDate ? formatDistanceToNow(new Date(product.auctionEndDate), { addSuffix: true }) : undefined
        };
    }, [product]);

    // Optimized handlers with useCallback
    const handleAddToCart = useCallback(async (productId: string, quantity: number) => {
        if (cart?.items.some(item => item.productId === productId) || isLoading || !product?.id || isLoadingCart) {
            console.warn('Item already exists in cart');
            return;
        }
        try {
            await addToCartMutation.mutateAsync({
                productId,
                quantity
            });
        } catch (error) {
            console.error('Failed to add to cart:', error);
        }
    }, [cart?.items, isLoading, product?.id, isLoadingCart, addToCartMutation]);

    const handleBuyNow = useCallback(async (productId: string, _quantity: number) => {
        try {
            router.push(`/checkout?productId=${productId}`);
        } catch (error) {
            console.error('Failed to buy now:', error);
        }
    }, [router]);

    const handlePlaceBid = useCallback(() => {
        console.log('Place bid for product:', product?.id);
    }, [product?.id]);

    const handleShowBidHistory = useCallback(() => {
        setIsBidHistoryOpen(true);
    }, []);

    const handleViewSalesHistory = useCallback(() => {
        console.log('View sales history for product:', product?.id);
    }, [product?.id]);

    // Memoized left content
    const leftContent = useMemo(() => {
        if (!transformedProduct) return null;
        return (
            <VStack gap={4} align="stretch">
                <Box px={{ base: 0, md: 6 }}>
                    <Breadcrumb items={breadcrumbItems} />
                </Box>

                <Box
                    position="sticky"
                    top={{ base: 4, md: 8 }}
                    px={{ base: 0, md: 6 }}
                >
                    <ProductImageGallery
                        item={transformedProduct}
                        boxSizeWatchList={6}
                        containerProps={{
                            height: { base: 'full', lg: '500px', xl: '650px' },
                        }}
                    />
                </Box>
            </VStack>
        );
    }, [breadcrumbItems, transformedProduct]);

    if (isLoading) {
        return <ProductDetailSkeleton />;
    }

    if (isError || !product || !transformedProduct) {
        return <ProductDetailError error={error} />;
    }

    const rightContent = (
        <>
            <ProductInfo
                title={product.itemName}
                subtitle={product.category?.name || '-'}
                mb={6}
            />

            {dataSession.status == "loading" ? (
                <Skeleton
                    width="full"
                    height="300px"
                    borderRadius="lg" />
            ) : (
                product.sellType === 'auction' ? (
                    <AuctionInfo
                        currentBid={product.currentBid ? Number(product.currentBid) : Number(product.priceUSD)}
                        startingPrice={Number(product.priceUSD)}
                        bidCount={product.bidCount ?? 0}
                        auctionStartDate={product.auctionStartDate || new Date().toISOString()}
                        auctionEndDate={product.auctionEndDate || new Date().toISOString()}
                        extendedBiddingEnabled={product.extendedBiddingEnabled ?? false}
                        extendedBiddingMinutes={product.extendedBiddingMinutes ?? 5}
                        extendedBiddingDuration={product.extendedBiddingDuration ?? 10}
                        auctionStatus={product.auctionStatus || 'active'}
                        timeLeft={product.timeLeft || 'N/A'}
                        productId={product.id}
                        productName={product.itemName}
                        onPlaceBid={handlePlaceBid}
                        onShowBidHistory={handleShowBidHistory}
                        mb={6}
                        isAuthenticated={session?.user?.id ? true : false}
                    />
                ) : (
                    <BuyNowInfo
                        price={Number(product.priceUSD)}
                        productId={product.id}
                        productName={product.itemName}
                        onAddToCart={handleAddToCart}
                        isInCart={cart?.items.some(item => item.productId === product.id)}
                        onBuyNow={handleBuyNow}
                        mb={6}
                        isAuthenticated={session?.user?.id ? true : false}
                    />
                )
            )}

            <Box as="hr" my={6} borderColor="gray.200" />

            <Box>
                <Heading as="h3" size="md" mb={3}>
                    Description
                </Heading>
                <Text fontSize="sm" mb={4}>
                    {product.description || 'No description available for this product.'}
                </Text>
            </Box>

            <Box as="hr" my={6} borderColor="gray.200" />

            <SalesHistory
                onLinkClick={handleViewSalesHistory}
            />
        </>
    );

    return (
        <Box>
            <Box>
                <ProductDetailLayout
                    leftContent={leftContent}
                    rightContent={rightContent}
                />
            </Box>

            <Box p={8} my={2} bg="white">
                <RelatedProducts
                    productId={product.id}
                    categoryId={product.categoryId}
                    sellerId={product.sellerId}
                    currentProductName={product.itemName}
                    limit={6}
                />
            </Box>

            {/* WebSocket Debug - Only show in development */}
            {/* {process.env.NODE_ENV === 'development' && (
                <Box p={8} my={2} bg="white">
                    <WebSocketDebug
                        productId={product.id}
                        userId={dataSession?.data?.user?.id}
                    />
                </Box>
            )} */}
        </Box>
    );
};

export default ProductDetailPage;